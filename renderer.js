const { ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * DOM Elements - Will be initialized after DOM loads
 */
let navLinks, pages, addClassForm, classesList, loadingIndicator;

/**
 * Application State
 */
let currentClasses = [];
let cachedAthletes = []; // Cache for athlete data

// Achievement system state
let allAchievements = [];
let athleteAchievements = {};

// Data refresh callbacks
let dataRefreshCallbacks = {
    athletes: [],
    classes: [],
    runSessions: []
};

// FIXED: ID generation system to prevent duplicates
let idCounters = {
    athlete: 0,
    run: 0,
    session: 0
};

/**
 * Generate unique athlete ID - FIXED VERSION
 */
function generateUniqueAthleteId() {
    const timestamp = Date.now();
    const counter = ++idCounters.athlete;
    const random = Math.random().toString(36).substr(2, 6);
    return `athlete-${timestamp}-${counter}-${random}`;
}

/**
 * Generate unique run session ID - FIXED VERSION
 */
function generateUniqueRunId() {
    const timestamp = Date.now();
    const counter = ++idCounters.run;
    const random = Math.random().toString(36).substr(2, 6);
    return `run-${timestamp}-${counter}-${random}`;
}

/**
 * Generate unique session ID - FIXED VERSION
 */
function generateUniqueSessionId(classId) {
    const timestamp = Date.now();
    const counter = ++idCounters.session;
    return `${classId}-${timestamp}-${counter}`;
}

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Ninja Score Tracker - Renderer process started');

    // Initialize DOM elements
    navLinks = document.querySelectorAll('.nav-link');
    pages = document.querySelectorAll('.page');
    addClassForm = document.getElementById('add-class-form');
    classesList = document.getElementById('classes-list');
    loadingIndicator = document.getElementById('loading');

    // Set up navigation
    setupNavigation();

    // Set up settings page
    setupSettingsPage();

    // Set up achievement system
    setupAchievementSystem();

    // Set up data synchronization
    setupDataSynchronization();

    // Run auto-recovery to ensure data consistency
    setTimeout(autoRecovery, 2000); // Run after 2 seconds to let everything load

    // Listen for run session updates to auto-refresh leaderboard
    ipcRenderer.on('run-sessions-updated', async (event, updatedRunSessions) => {
        console.log('🔄 Received run sessions update notification - refreshing leaderboard');

        // Check if leaderboard page is currently active
        const leaderboardPage = document.getElementById('leaderboard-page');
        if (leaderboardPage && leaderboardPage.classList.contains('active')) {
            console.log('📊 Leaderboard is active, refreshing...');
            await loadLeaderboard();

            // Show brief notification
            showNotification('Leaderboard updated with new scores! 🏆', 'success');
        }
    });

    // Set up form handling
    setupFormHandling();
    setupAthleteFormHandling();

    // Set today's date as default
    setDefaultDate();

    // Load existing classes
    await loadClasses();
});

/**
 * Set up navigation between pages
 */
function setupNavigation() {
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            // Remove active class from all links and pages
            navLinks.forEach(l => l.classList.remove('active'));
            pages.forEach(p => p.classList.remove('active'));

            // Add active class to clicked link
            link.classList.add('active');

            // Show corresponding page
            const pageId = link.getAttribute('data-page') + '-page';
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');

                // Load leaderboard data when leaderboard page is shown
                if (pageId === 'leaderboard-page') {
                    loadLeaderboard();
                }

                // Load run page data when run page is shown
                if (pageId === 'run-page') {
                    loadRunPage();
                }
            }
        });
    });
}

/**
 * Set up form handling for adding new classes
 */
function setupFormHandling() {
    if (!addClassForm) {
        console.error('Add class form not found');
        return;
    }

    // Ensure className input is properly configured
    const classNameInput = document.getElementById('className');
    if (classNameInput) {
        // Ensure input is interactive
        classNameInput.disabled = false;
        classNameInput.readOnly = false;
        classNameInput.style.pointerEvents = 'auto';
        classNameInput.style.userSelect = 'text';
    }

    addClassForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(addClassForm);
        const newClass = {
            name: formData.get('className').trim(),
            date: formData.get('classDate'),
            time: formData.get('classTime'),
            description: formData.get('classDescription').trim()
        };
        
        // Validate required fields
        if (!newClass.name || !newClass.date || !newClass.time) {
            showNotification('Please fill in all required fields', 'error');
            return;
        }
        
        // Show loading
        showLoading(true);
        
        try {
            // Save class via IPC
            const result = await ipcRenderer.invoke('save-class', newClass);
            
            if (result.success) {
                // Add to local state
                currentClasses.push(result.class);
                
                // Update UI
                await renderClasses();
                
                // Reset form
                addClassForm.reset();
                setDefaultDate();
                
                // Show success message
                showNotification('Class added successfully!', 'success');
            } else {
                showNotification('Failed to save class: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Error saving class:', error);
            showNotification('An error occurred while saving the class', 'error');
        } finally {
            showLoading(false);
        }
    });
}

/**
 * Set up athlete form handling for adding new athletes
 */
function setupAthleteFormHandling() {
    const addAthleteFormHome = document.getElementById('add-athlete-form-home');

    if (!addAthleteFormHome) {
        console.error('Add athlete form not found');
        return;
    }

    addAthleteFormHome.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Get form data
        const formData = new FormData(addAthleteFormHome);
        const athleteName = formData.get('athleteName').trim();

        // Check if athlete with same name already exists
        try {
            const existingAthletes = await ipcRenderer.invoke('load-athletes');
            const duplicateAthlete = existingAthletes.find(athlete =>
                athlete.name.toLowerCase() === athleteName.toLowerCase()
            );

            if (duplicateAthlete) {
                showNotification(`Athlete "${athleteName}" already exists in the system`, 'error');
                return;
            }
        } catch (error) {
            console.error('Error checking for duplicate athletes:', error);
        }

        // Handle avatar file
        const avatarFile = formData.get('athleteAvatar');
        let avatarPath = 'placeholder-avatar.png'; // Default placeholder

        if (avatarFile && avatarFile.size > 0) {
            // For now, just store the filename
            // In a real app, you'd save the file to a directory
            avatarPath = avatarFile.name;
        }

        const newAthlete = {
            id: generateUniqueAthleteId(),
            name: athleteName,
            age: parseInt(formData.get('athleteAge')),
            nickname: formData.get('athleteNickname').trim(),
            level: formData.get('athleteLevel'),
            description: formData.get('athleteDescription').trim(),
            avatar: avatarPath,
            classIds: [], // Start with empty array
            sessionsAttended: 0,
            createdAt: new Date().toISOString()
        };

        // Validate required fields
        if (!newAthlete.name || !newAthlete.age || !newAthlete.level) {
            showNotification('Please fill in all required fields (Name, Age, Level)', 'error');
            return;
        }

        // Show loading
        showLoading(true);

        try {
            // Load existing athletes
            const allAthletes = await ipcRenderer.invoke('load-athletes');

            // Add new athlete
            allAthletes.push(newAthlete);

            // Save to storage
            const result = await ipcRenderer.invoke('save-athletes', allAthletes);

            if (result.success) {
                // Reset form
                addAthleteFormHome.reset();

                // Show success message
                showNotification(`Athlete "${newAthlete.name}" added successfully!`, 'success');

                // Trigger global data refresh
                await refreshAthleteData();
            } else {
                showNotification('Failed to save athlete', 'error');
            }
        } catch (error) {
            console.error('Error saving athlete:', error);
            showNotification('An error occurred while saving the athlete', 'error');
        } finally {
            showLoading(false);
        }
    });
}

/**
 * Set default date to today
 */
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('classDate').value = today;
}

/**
 * Load classes from storage
 */
async function loadClasses() {
    showLoading(true);
    
    try {
        currentClasses = await ipcRenderer.invoke('load-classes');
        await renderClasses();
    } catch (error) {
        console.error('Error loading classes:', error);
        showNotification('Failed to load classes', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Render classes in the UI
 */
async function renderClasses() {
    if (currentClasses.length === 0) {
        classesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <p style="color: #666; font-size: 1.1rem;">No classes yet. Add your first class above!</p>
            </div>
        `;
        return;
    }

    // Always load fresh athlete data for accurate counts
    let allAthletes = [];
    try {
        allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        cachedAthletes = allAthletes; // Update cache with fresh data
    } catch (error) {
        console.error('Error loading athletes for class display:', error);
        allAthletes = [];
    }
    
    // Sort classes by date and time (newest first)
    const sortedClasses = [...currentClasses].sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateB - dateA;
    });
    
    classesList.innerHTML = sortedClasses.map(classItem => {
        // Calculate actual athlete count for this class
        const actualAthleteCount = allAthletes.filter(athlete =>
            athlete.classIds && athlete.classIds.includes(classItem.id)
        ).length;

        return `
            <div class="class-card" data-class-id="${classItem.id}">
                <div class="class-card-header">
                    <div>
                        <div class="class-card-title">
                            <i class="fas fa-graduation-cap"></i>
                            ${escapeHtml(classItem.name)}
                        </div>
                        <div class="class-card-meta">
                            <span>
                                <i class="fas fa-calendar"></i>
                                ${formatDate(classItem.date)}
                            </span>
                            <span>
                                <i class="fas fa-clock"></i>
                                ${formatTime(classItem.time)}
                            </span>
                        </div>
                    </div>
                </div>

                ${classItem.description ? `
                    <div class="class-card-description">
                        <i class="fas fa-align-left"></i>
                        ${escapeHtml(classItem.description)}
                    </div>
                ` : ''}

                <div class="class-card-footer">
                    <div class="athlete-count">
                        <i class="fas fa-users"></i>
                        ${actualAthleteCount} athlete${actualAthleteCount !== 1 ? 's' : ''}
                    </div>
                    <button class="btn btn-secondary" onclick="viewRoster('${classItem.id}')">
                        <i class="fas fa-list-ul"></i>
                        View Roster
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Format time for display
 */
function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
    if (!loadingIndicator) return;

    if (show) {
        loadingIndicator.classList.remove('hidden');
    } else {
        loadingIndicator.classList.add('hidden');
    }
}

/**
 * Notification Queue System
 */
let notificationQueue = [];
let isProcessingNotifications = false;

/**
 * Show notification with queue management to prevent overlapping
 */
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);

    // Add to queue
    notificationQueue.push({ message, type });

    // Process queue if not already processing
    if (!isProcessingNotifications) {
        processNotificationQueue();
    }
}

/**
 * Process notification queue one by one
 */
async function processNotificationQueue() {
    if (notificationQueue.length === 0) {
        isProcessingNotifications = false;
        return;
    }

    isProcessingNotifications = true;

    const { message, type } = notificationQueue.shift();

    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
    `;

    // Position toast in notification container
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }

    notificationContainer.appendChild(toast);

    // Auto-remove toast after duration based on message length
    const duration = Math.max(1500, Math.min(4000, message.length * 40)); // 1.5-4 seconds based on length

    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
            // Process next notification after a short delay
            setTimeout(() => processNotificationQueue(), 150);
        }, 300);
    }, duration);
}

/**
 * View roster for a class
 */
async function viewRoster(classId) {
    const classItem = currentClasses.find(c => c.id === classId);
    if (!classItem) {
        showNotification('Class not found', 'error');
        return;
    }

    try {
        // Always load fresh data for roster to ensure accuracy
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        cachedAthletes = allAthletes; // Update cache with fresh data

        const classAthletes = allAthletes.filter(athlete =>
            athlete.classIds && athlete.classIds.includes(classId)
        );

        // Always show roster modal, even if empty
        showAthleteRosterModal(classItem, classAthletes, allAthletes);

    } catch (error) {
        console.error('Error loading class roster:', error);
        showNotification('Failed to load class roster', 'error');
    }
}

/**
 * Show athlete roster modal
 */
function showAthleteRosterModal(classItem, athletes, allAthletes) {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'roster-modal-overlay';

    const hasAthletes = athletes.length > 0;
    const availableAthletes = allAthletes.filter(athlete =>
        !athlete.classIds || !athlete.classIds.includes(classItem.id)
    );

    modal.innerHTML = `
        <div class="roster-modal">
            <div class="roster-modal-header">
                <h3><i class="fas fa-users"></i> ${escapeHtml(classItem.name)} - Athletes (${athletes.length})</h3>
                <div class="roster-modal-actions">
                    ${availableAthletes.length > 0 ? `
                        <button class="btn btn-primary" onclick="openAddAthletesToClassModal('${classItem.id}')">
                            <i class="fas fa-user-plus"></i> Add Athletes
                        </button>
                    ` : ''}
                    <button class="close-modal-btn" onclick="this.closest('.roster-modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="roster-modal-body">
                ${hasAthletes ? `
                    <div class="athletes-grid">
                        ${athletes.map(athlete => `
                            <div class="athlete-card" onclick="viewAthleteProfile('${athlete.id}')">
                                <div class="athlete-avatar">
                                    ${athlete.avatar && athlete.avatar !== 'placeholder-avatar.png' ?
                                        `<img src="${athlete.avatar}" alt="${athlete.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` :
                                        getAthleteInitials(athlete.name)
                                    }
                                </div>
                                <div class="athlete-info">
                                    <h4>${escapeHtml(athlete.name)}</h4>
                                    ${athlete.nickname ? `<div class="athlete-nickname">"${escapeHtml(athlete.nickname)}"</div>` : ''}
                                    <div class="athlete-level">${athlete.level ? athlete.level.charAt(0).toUpperCase() + athlete.level.slice(1) : 'Beginner'}</div>
                                </div>
                                <div class="view-profile-btn">
                                    <i class="fas fa-user"></i>
                                    View Profile
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="empty-roster-state">
                        <i class="fas fa-users" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                        <h4>No Athletes in This Class</h4>
                        <p>This class doesn't have any athletes assigned yet.</p>
                        ${availableAthletes.length > 0 ? `
                            <button class="btn btn-primary" onclick="openAddAthletesToClassModal('${classItem.id}')">
                                <i class="fas fa-user-plus"></i> Add Your First Athletes
                            </button>
                        ` : `
                            <p style="color: #999; font-style: italic;">Create some athletes first to add them to classes.</p>
                        `}
                    </div>
                `}
            </div>
        </div>
    `;

    // Add to page
    document.body.appendChild(modal);

    // Close on overlay click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Open modal to add athletes to a class
 */
async function openAddAthletesToClassModal(classId) {
    try {
        // Load fresh data
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const classItem = currentClasses.find(c => c.id === classId);

        if (!classItem) {
            showNotification('Class not found', 'error');
            return;
        }

        // Filter out athletes already in this class
        const availableAthletes = allAthletes.filter(athlete =>
            !athlete.classIds || !athlete.classIds.includes(classId)
        );

        if (availableAthletes.length === 0) {
            showNotification('No available athletes to add to this class', 'info');
            return;
        }

        showAddAthletesToClassModal(classItem, availableAthletes);

    } catch (error) {
        console.error('Error loading athletes for class assignment:', error);
        showNotification('Failed to load available athletes', 'error');
    }
}

/**
 * Show the add athletes to class modal
 */
function showAddAthletesToClassModal(classItem, availableAthletes) {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'add-athletes-modal-overlay';
    modal.innerHTML = `
        <div class="add-athletes-modal">
            <div class="add-athletes-modal-header">
                <h3><i class="fas fa-user-plus"></i> Add Athletes to ${escapeHtml(classItem.name)}</h3>
                <button class="close-modal-btn" onclick="this.closest('.add-athletes-modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="add-athletes-modal-body">
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="athlete-search" placeholder="Search athletes by name..."
                               oninput="filterAvailableAthletes(this.value)">
                    </div>
                    <div class="selection-controls">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="selectAllAthletes(true)">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="selectAllAthletes(false)">
                            <i class="fas fa-square"></i> Select None
                        </button>
                    </div>
                </div>

                <div class="available-athletes-list" id="available-athletes-list">
                    ${availableAthletes.map(athlete => `
                        <div class="available-athlete-item" data-athlete-id="${athlete.id}" data-athlete-name="${athlete.name.toLowerCase()}">
                            <label class="athlete-checkbox-label">
                                <input type="checkbox" class="athlete-checkbox" value="${athlete.id}">
                                <div class="athlete-item-content">
                                    <div class="athlete-avatar-small">
                                        ${athlete.avatar && athlete.avatar !== 'placeholder-avatar.png' ?
                                            `<img src="${athlete.avatar}" alt="${athlete.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` :
                                            `<span>${getAthleteInitials(athlete.name)}</span>`
                                        }
                                    </div>
                                    <div class="athlete-details">
                                        <h5>${escapeHtml(athlete.name)}</h5>
                                        ${athlete.nickname ? `<span class="athlete-nickname">"${escapeHtml(athlete.nickname)}"</span>` : ''}
                                        <span class="athlete-level">${athlete.level ? athlete.level.charAt(0).toUpperCase() + athlete.level.slice(1) : 'Beginner'}</span>
                                    </div>
                                </div>
                            </label>
                        </div>
                    `).join('')}
                </div>

                <div class="add-athletes-modal-footer">
                    <div class="selected-count">
                        <span id="selected-count">0</span> athlete(s) selected
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.add-athletes-modal-overlay').remove()">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" onclick="addSelectedAthletesToClass('${classItem.id}')" id="add-athletes-btn" disabled>
                            <i class="fas fa-plus"></i> Add Selected Athletes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add to page
    document.body.appendChild(modal);

    // Set up event listeners for checkboxes
    const checkboxes = modal.querySelectorAll('.athlete-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Close on overlay click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // Focus search box
    const searchBox = modal.querySelector('#athlete-search');
    if (searchBox) {
        setTimeout(() => searchBox.focus(), 100);
    }
}

/**
 * Filter available athletes by search term
 */
function filterAvailableAthletes(searchTerm) {
    const athleteItems = document.querySelectorAll('.available-athlete-item');
    const term = searchTerm.toLowerCase().trim();

    athleteItems.forEach(item => {
        const athleteName = item.dataset.athleteName;
        if (athleteName.includes(term)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * Select or deselect all visible athletes
 */
function selectAllAthletes(select) {
    const visibleCheckboxes = document.querySelectorAll('.available-athlete-item:not([style*="display: none"]) .athlete-checkbox');

    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = select;
    });

    updateSelectedCount();
}

/**
 * Update the selected count display
 */
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.athlete-checkbox:checked');
    const countElement = document.getElementById('selected-count');
    const addButton = document.getElementById('add-athletes-btn');

    if (countElement) {
        countElement.textContent = checkboxes.length;
    }

    if (addButton) {
        addButton.disabled = checkboxes.length === 0;
    }
}

/**
 * Add selected athletes to the class
 */
async function addSelectedAthletesToClass(classId) {
    const selectedCheckboxes = document.querySelectorAll('.athlete-checkbox:checked');
    const selectedAthleteIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedAthleteIds.length === 0) {
        showNotification('Please select at least one athlete', 'warning');
        return;
    }

    showLoading(true);

    try {
        // Load current athletes data
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];

        // Update selected athletes to include this class
        let updatedCount = 0;
        selectedAthleteIds.forEach(athleteId => {
            const athlete = allAthletes.find(a => a.id === athleteId);
            if (athlete) {
                if (!athlete.classIds) {
                    athlete.classIds = [];
                }
                if (!athlete.classIds.includes(classId)) {
                    athlete.classIds.push(classId);
                    updatedCount++;
                }
            }
        });

        // Save updated athletes
        const result = await ipcRenderer.invoke('save-athletes', allAthletes);

        if (result.success) {
            // Close the add athletes modal
            const addModal = document.querySelector('.add-athletes-modal-overlay');
            if (addModal) {
                addModal.remove();
            }

            // Refresh the roster modal
            const rosterModal = document.querySelector('.roster-modal-overlay');
            if (rosterModal) {
                rosterModal.remove();
            }

            // Reopen the roster to show updated list
            const classItem = currentClasses.find(c => c.id === classId);
            if (classItem) {
                await viewRoster(classId);
            }

            // Show success message
            showNotification(`Successfully added ${updatedCount} athlete(s) to the class!`, 'success');

            // Trigger global data refresh
            await refreshAthleteData();

        } else {
            showNotification('Failed to add athletes to class', 'error');
        }

    } catch (error) {
        console.error('Error adding athletes to class:', error);
        showNotification('An error occurred while adding athletes', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * View individual athlete profile
 */
async function viewAthleteProfile(athleteId) {
    try {
        // Close any open modals
        const modals = document.querySelectorAll('.roster-modal-overlay');
        modals.forEach(modal => modal.remove());

        // Always load fresh athlete data to ensure accuracy
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        cachedAthletes = allAthletes; // Update cache with fresh data

        const athlete = allAthletes.find(a => a.id === athleteId);

        if (!athlete) {
            showNotification('Athlete not found', 'error');
            return;
        }

        // Load athlete's run sessions for stats
        const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];
        const athleteRuns = allRunSessions.filter(run => run.athleteId === athleteId);

        // Navigate to profile page
        navigateToPage('athlete-profile-page');

        // Populate profile data
        populateAthleteProfile(athlete, athleteRuns);

    } catch (error) {
        console.error('Error loading athlete profile:', error);
        showNotification('Failed to load athlete profile', 'error');
    }
}

/**
 * Load and render leaderboard data
 */
async function loadLeaderboard() {
    const leaderboardList = document.getElementById('leaderboard-list');
    const emptyLeaderboard = document.getElementById('empty-leaderboard');
    const totalAthletesEl = document.getElementById('total-athletes-lb');
    const lastUpdatedEl = document.getElementById('last-updated-lb');

    if (!leaderboardList) return;

    showLoading(true);

    try {
        // Load run sessions first
        console.log('🔄 Loading run sessions for leaderboard...');
        const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];
        console.log(`✅ Loaded ${allRunSessions.length} run sessions`);

        // Load all athletes
        let allAthletes = await ipcRenderer.invoke('load-athletes');
        console.log(`✅ Loaded ${allAthletes.length} athletes`);

        if (allAthletes.length === 0) {
            // Show empty state if no athletes
            leaderboardList.style.display = 'none';
            emptyLeaderboard.classList.remove('hidden');
            totalAthletesEl.textContent = '0 Athletes';
            return;
        }

        // Calculate live scores for each athlete
        allAthletes = allAthletes.map(athlete => {
            // Find all run sessions for this athlete and sum their scores
            const athleteRuns = allRunSessions.filter(run => run.athleteId === athlete.id);
            const totalScore = athleteRuns.reduce((sum, run) => sum + (run.score || 0), 0);

            console.log(`🏃 ${athlete.name} (${athlete.id}): ${athleteRuns.length} runs, total score: ${totalScore}`);

            return {
                ...athlete,
                score: totalScore, // Calculate live score from run sessions
                xp: Math.floor(Math.random() * 100) + 1,
                maxXp: 100,
                level: athlete.level ? `Level ${getLevelNumber(athlete.level)}` : 'Level 1'
            };
        });

        // Sort by score (descending), then by XP
        allAthletes.sort((a, b) => {
            if (b.score !== a.score) return b.score - a.score;
            return b.xp - a.xp;
        });

        // Update stats
        totalAthletesEl.textContent = `${allAthletes.length} Athletes`;
        updateLastUpdated();

        if (allAthletes.length === 0) {
            leaderboardList.style.display = 'none';
            emptyLeaderboard.classList.remove('hidden');
        } else {
            leaderboardList.style.display = 'block';
            emptyLeaderboard.classList.add('hidden');

            // Render athletes
            leaderboardList.innerHTML = allAthletes.map((athlete, index) => {
                const rank = index + 1;
                const isTop3 = rank <= 3;

                return `
                    <div class="leaderboard-athlete">
                        <div class="athlete-rank ${isTop3 ? 'top-3' : ''}">
                            ${rank}
                            ${isTop3 ? getMedalIcon(rank) : ''}
                        </div>

                        <div class="leaderboard-avatar">
                            ${athlete.avatar && athlete.avatar !== 'placeholder-avatar.png' ?
                                `<img src="${athlete.avatar}" alt="${athlete.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` :
                                getAthleteInitials(athlete.name)
                            }
                        </div>

                        <div class="athlete-info-lb">
                            <div class="athlete-name-lb">${escapeHtml(athlete.name)}</div>
                            ${athlete.nickname ? `<div class="athlete-nickname-lb">"${escapeHtml(athlete.nickname)}"</div>` : ''}
                            <div class="athlete-level-lb">${athlete.level}</div>

                            <div class="xp-container">
                                <div class="xp-label">Experience Points</div>
                                <div class="xp-bar">
                                    <div class="xp-progress" style="width: ${(athlete.xp / athlete.maxXp) * 100}%"></div>
                                </div>
                                <div class="xp-text">${athlete.xp}/${athlete.maxXp} XP</div>
                            </div>
                        </div>

                        <div class="athlete-score">
                            <div class="score-value">${athlete.score}</div>
                            <div class="score-label">Total Score</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

    } catch (error) {
        console.error('Error loading leaderboard:', error);
        showNotification('Failed to load leaderboard data', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Get mock athletes for demonstration
 */
function getMockAthletes() {
    return [
        { name: 'Alex Thunder', nickname: 'Lightning', level: 'Level 3', score: 0, xp: 85, maxXp: 100 },
        { name: 'Maya Storm', nickname: 'Shadow', level: 'Level 2', score: 0, xp: 60, maxXp: 100 },
        { name: 'Jordan Blaze', nickname: 'Rocket', level: 'Level 4', score: 0, xp: 95, maxXp: 100 },
        { name: 'Sam Frost', nickname: 'Ice', level: 'Level 1', score: 0, xp: 30, maxXp: 100 },
        { name: 'Casey Wind', nickname: 'Breeze', level: 'Level 2', score: 0, xp: 70, maxXp: 100 }
    ];
}

/**
 * Convert level string to number
 */
function getLevelNumber(level) {
    switch (level.toLowerCase()) {
        case 'beginner': return 1;
        case 'intermediate': return 2;
        case 'advanced': return 3;
        default: return 1;
    }
}

/**
 * Get medal icon for top 3
 */
function getMedalIcon(rank) {
    const medals = {
        1: '<i class="fas fa-crown medal-icon medal-gold"></i>',
        2: '<i class="fas fa-medal medal-icon medal-silver"></i>',
        3: '<i class="fas fa-medal medal-icon medal-bronze"></i>'
    };
    return medals[rank] || '';
}

/**
 * Update last updated timestamp
 */
function updateLastUpdated() {
    const lastUpdatedEl = document.getElementById('last-updated-lb');
    if (lastUpdatedEl) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        lastUpdatedEl.textContent = `Last Updated: ${timeString}`;
    }
}

/**
 * Get athlete initials for avatar
 */
function getAthleteInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

/**
 * Run Page Functionality
 */
let currentRunClass = null;
let currentRunAthletes = [];
let sessionStartTime = null;
let sessionTimer = null;
let runSessions = [];
let athleteScores = {}; // Individual athlete scores
let classTimer = {
    startTime: null,
    elapsedTime: 0,
    isRunning: false,
    interval: null,
    duration: 300 // 5 minutes default in seconds
};

/**
 * Load and initialize the run page
 */
async function loadRunPage() {
    console.log('Loading Run page...');

    // Load classes for selection
    await loadClassesForRun();

    // Set up event listeners
    setupRunPageEventListeners();

    // Load existing run sessions
    await loadRunSessions();
}

/**
 * Load classes into the run page selector
 */
async function loadClassesForRun() {
    const classSelect = document.getElementById('run-class-select');
    if (!classSelect) return;

    try {
        const classes = await ipcRenderer.invoke('load-classes');

        // Clear existing options except the first one
        classSelect.innerHTML = '<option value="">Choose a class to start tracking...</option>';

        // Add classes to dropdown
        classes.forEach(classItem => {
            const option = document.createElement('option');
            option.value = classItem.id;
            option.textContent = `${classItem.name} - ${formatDate(classItem.date)} at ${formatTime(classItem.time)}`;
            classSelect.appendChild(option);
        });

        console.log(`Loaded ${classes.length} classes for run page`);

    } catch (error) {
        console.error('Error loading classes for run page:', error);
        showNotification('Failed to load classes', 'error');
    }
}

/**
 * Set up event listeners for run page
 */
function setupRunPageEventListeners() {
    const classSelect = document.getElementById('run-class-select');
    const loadClassBtn = document.getElementById('load-class-btn');
    const startSessionBtn = document.getElementById('start-session-btn');
    const endSessionBtn = document.getElementById('end-session-btn');
    const saveAllBtn = document.getElementById('save-all-scores-btn');
    const clearAllBtn = document.getElementById('clear-all-scores-btn');

    if (classSelect) {
        classSelect.addEventListener('change', (e) => {
            const selectedClassId = e.target.value;
            if (loadClassBtn) {
                loadClassBtn.disabled = !selectedClassId;
            }
        });
    }

    if (loadClassBtn) {
        loadClassBtn.addEventListener('click', loadSelectedClass);
    }

    if (startSessionBtn) {
        startSessionBtn.addEventListener('click', startSession);
    }

    if (endSessionBtn) {
        endSessionBtn.addEventListener('click', endSession);
    }

    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllScores);
    }

    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearAllScores);
    }
}

/**
 * Load the selected class and its athletes
 */
async function loadSelectedClass() {
    const classSelect = document.getElementById('run-class-select');
    const selectedClassId = classSelect.value;

    if (!selectedClassId) return;

    showLoading(true);

    try {
        // Load class details
        const classes = await ipcRenderer.invoke('load-classes');
        currentRunClass = classes.find(c => c.id === selectedClassId);

        if (!currentRunClass) {
            showNotification('Class not found', 'error');
            return;
        }

        // Always load fresh athlete data for accurate class roster
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        cachedAthletes = allAthletes; // Update cache with fresh data

        currentRunAthletes = allAthletes.filter(athlete =>
            athlete.classIds && athlete.classIds.includes(selectedClassId)
        );

        // Display class info
        displaySelectedClassInfo();

        // Show empty state or class info
        if (currentRunAthletes.length === 0) {
            showNotification('No athletes found in this class', 'error');
        } else {
            showNotification(`Loaded ${currentRunAthletes.length} athletes`, 'success');
        }

    } catch (error) {
        console.error('Error loading selected class:', error);
        showNotification('Failed to load class data', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Display selected class information
 */
function displaySelectedClassInfo() {
    const classInfoSection = document.getElementById('selected-class-info');
    const className = document.getElementById('selected-class-name');
    const classDate = document.getElementById('selected-class-date');
    const classTime = document.getElementById('selected-class-time');
    const athleteCount = document.getElementById('selected-athlete-count');
    const emptyState = document.getElementById('run-empty-state');

    if (currentRunClass && classInfoSection) {
        classInfoSection.classList.remove('hidden');
        if (emptyState) emptyState.classList.add('hidden');

        if (className) className.textContent = currentRunClass.name;
        if (classDate) classDate.innerHTML = `<i class="fas fa-calendar"></i> ${formatDate(currentRunClass.date)}`;
        if (classTime) classTime.innerHTML = `<i class="fas fa-clock"></i> ${formatTime(currentRunClass.time)}`;
        if (athleteCount) athleteCount.innerHTML = `<i class="fas fa-users"></i> ${currentRunAthletes.length} Athletes`;
    }
}

/**
 * Start a tracking session
 */
function startSession() {
    if (!currentRunClass || currentRunAthletes.length === 0) {
        showNotification('Please load a class with athletes first', 'error');
        return;
    }

    sessionStartTime = new Date();

    // Update UI
    const startBtn = document.getElementById('start-session-btn');
    const endBtn = document.getElementById('end-session-btn');
    const trackingSection = document.getElementById('athletes-tracking-section');

    if (startBtn) startBtn.classList.add('hidden');
    if (endBtn) endBtn.classList.remove('hidden');
    if (trackingSection) trackingSection.classList.remove('hidden');

    // Start timer
    startSessionTimer();

    // Render athlete tracking cards
    renderAthleteTrackingCards();

    showNotification('Session started! Begin tracking athlete performance.', 'success');
}

/**
 * End the tracking session
 */
function endSession() {
    if (sessionTimer) {
        clearInterval(sessionTimer);
        sessionTimer = null;
    }

    sessionStartTime = null;

    // Update UI
    const startBtn = document.getElementById('start-session-btn');
    const endBtn = document.getElementById('end-session-btn');
    const trackingSection = document.getElementById('athletes-tracking-section');

    if (startBtn) startBtn.classList.remove('hidden');
    if (endBtn) endBtn.classList.add('hidden');
    if (trackingSection) trackingSection.classList.add('hidden');

    showNotification('Session ended', 'success');
}

/**
 * Start the session timer
 */
function startSessionTimer() {
    const timerElement = document.getElementById('session-timer');
    if (!timerElement) return;

    sessionTimer = setInterval(() => {
        if (sessionStartTime) {
            const elapsed = Math.floor((new Date() - sessionStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

/**
 * Render athlete tracking cards
 */
function renderAthleteTrackingCards() {
    const trackingGrid = document.getElementById('athletes-tracking-grid');
    if (!trackingGrid) return;

    trackingGrid.innerHTML = currentRunAthletes.map(athlete => `
        <div class="athlete-tracking-card" data-athlete-id="${athlete.id}">
            <div class="athlete-card-header">
                <div class="athlete-tracking-avatar">
                    ${athlete.avatar && athlete.avatar !== 'placeholder-avatar.png' ?
                        `<img src="${athlete.avatar}" alt="${athlete.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` :
                        getAthleteInitials(athlete.name)
                    }
                </div>
                <div class="athlete-tracking-info">
                    <h4>${escapeHtml(athlete.name)}</h4>
                    ${athlete.nickname ? `<div class="nickname">"${escapeHtml(athlete.nickname)}"</div>` : ''}
                    <div class="level">${athlete.level ? athlete.level.charAt(0).toUpperCase() + athlete.level.slice(1) : 'Beginner'}</div>
                </div>
                <div class="athlete-current-score">
                    <div class="score-display">
                        <span class="score-value" id="current-score-${athlete.id}">0</span>
                        <span class="score-label">POINTS</span>
                    </div>
                </div>
            </div>


            <!-- Scoring Buttons -->
            <div class="scoring-section">
                <div class="scoring-label">Live Scoring</div>
                <div class="scoring-buttons">
                    <button class="score-btn plus-btn" onclick="addScore('${athlete.id}', 1)" title="Small Achievement">+${currentSettings.smallPoints}</button>
                    <button class="score-btn plus-btn" onclick="addScore('${athlete.id}', 2)" title="Medium Achievement">+${currentSettings.mediumPoints}</button>
                    <button class="score-btn plus-btn" onclick="addScore('${athlete.id}', 5)" title="Large Achievement">+${currentSettings.largePoints}</button>
                    <button class="score-btn plus-btn" onclick="addScore('${athlete.id}', 10)" title="Mega Achievement">+${currentSettings.megaPoints}</button>
                    <button class="score-btn mystery-btn" onclick="addMysteryScore('${athlete.id}')" title="Random ${currentSettings.mysteryMin}-${currentSettings.mysteryMax} points">
                        <i class="fas fa-question"></i>
                    </button>
                    <button class="score-btn miss-btn" onclick="addMiss('${athlete.id}')" title="Missed Target (-${currentSettings.missPenalty} point${currentSettings.missPenalty !== 1 ? 's' : ''})">
                        <i class="fas fa-times"></i>
                        Miss
                    </button>
                </div>
            </div>

            <div class="notes-section">
                <label for="notes-${athlete.id}">Performance Notes</label>
                <textarea id="notes-${athlete.id}" class="notes-textarea"
                          placeholder="Add feedback, observations, or notes..."></textarea>
            </div>

            <button class="submit-athlete-btn" onclick="submitAthleteRun('${athlete.id}')" id="submit-btn-${athlete.id}">
                <i class="fas fa-check"></i>
                Submit Run
            </button>
        </div>
    `).join('');

    // Initialize athlete data
    currentRunAthletes.forEach(athlete => {
        if (!athleteScores[athlete.id]) {
            athleteScores[athlete.id] = 0;
        }
    });

    // Initialize class timer controls
    initializeClassTimer();
}

/**
 * Initialize class timer controls
 */
function initializeClassTimer() {
    const startBtn = document.getElementById('class-timer-start');
    const stopBtn = document.getElementById('class-timer-stop');
    const resetBtn = document.getElementById('class-timer-reset');
    const minutesSelect = document.getElementById('timer-minutes-select');

    if (startBtn) {
        startBtn.addEventListener('click', startClassTimer);
    }
    if (stopBtn) {
        stopBtn.addEventListener('click', stopClassTimer);
    }
    if (resetBtn) {
        resetBtn.addEventListener('click', resetClassTimer);
    }
    if (minutesSelect) {
        minutesSelect.addEventListener('change', updateClassTimerDuration);
    }

    // Initialize duration display
    updateClassTimerDuration();
}

/**
 * Start class timer
 */
function startClassTimer() {
    if (classTimer.isRunning) return;

    classTimer.startTime = Date.now() - (classTimer.elapsedTime * 1000);
    classTimer.isRunning = true;

    // Update UI
    const startBtn = document.getElementById('class-timer-start');
    const stopBtn = document.getElementById('class-timer-stop');
    const minutesSelect = document.getElementById('timer-minutes-select');

    if (startBtn) startBtn.disabled = true;
    if (stopBtn) stopBtn.disabled = false;
    if (minutesSelect) minutesSelect.disabled = true;

    // Start the timer interval
    classTimer.interval = setInterval(updateClassTimer, 100);

    showNotification('Class timer started! 🚀', 'success');
}

/**
 * Stop class timer
 */
function stopClassTimer() {
    if (!classTimer.isRunning) return;

    classTimer.isRunning = false;
    if (classTimer.interval) {
        clearInterval(classTimer.interval);
        classTimer.interval = null;
    }

    // Update UI
    const startBtn = document.getElementById('class-timer-start');
    const stopBtn = document.getElementById('class-timer-stop');
    const minutesSelect = document.getElementById('timer-minutes-select');

    if (startBtn) startBtn.disabled = false;
    if (stopBtn) stopBtn.disabled = true;
    if (minutesSelect) minutesSelect.disabled = false;

    showNotification('Class timer stopped! ⏸️', 'info');
}

/**
 * Reset class timer
 */
function resetClassTimer() {
    // Stop if running
    if (classTimer.isRunning) {
        stopClassTimer();
    }

    classTimer.elapsedTime = 0;
    updateClassTimer();

    showNotification('Class timer reset! 🔄', 'info');
}

/**
 * Update class timer duration from dropdown
 */
function updateClassTimerDuration() {
    const minutesSelect = document.getElementById('timer-minutes-select');
    if (!minutesSelect) return;

    const minutes = parseInt(minutesSelect.value);
    classTimer.duration = minutes * 60; // Convert to seconds

    // Update duration display
    const durationDisplay = document.getElementById('class-timer-duration');
    if (durationDisplay) {
        durationDisplay.textContent = `/ ${minutes}:00`;
    }

    // Update progress if timer is not running
    if (!classTimer.isRunning) {
        updateClassTimer();
    }
}

/**
 * Update class timer display
 */
function updateClassTimer() {
    if (classTimer.isRunning) {
        classTimer.elapsedTime = (Date.now() - classTimer.startTime) / 1000;
    }

    const timerDisplay = document.getElementById('class-timer-display');
    const progressBar = document.getElementById('class-timer-progress');

    if (timerDisplay) {
        const minutes = Math.floor(classTimer.elapsedTime / 60);
        const seconds = Math.floor(classTimer.elapsedTime % 60);
        timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Add pulsing effect when running
        if (classTimer.isRunning) {
            timerDisplay.style.animation = 'timer-pulse 1s ease-in-out infinite';
        } else {
            timerDisplay.style.animation = 'none';
        }
    }

    if (progressBar) {
        const progressPercent = Math.min((classTimer.elapsedTime / classTimer.duration) * 100, 100);
        progressBar.style.width = `${progressPercent}%`;

        // Change color as time progresses
        if (progressPercent >= 100) {
            progressBar.style.background = 'linear-gradient(90deg, #FF0000, #FF6B6B)';
            if (classTimer.isRunning) {
                // Timer completed - show notification and stop
                stopClassTimer();
                showNotification('⏰ Time\'s up! Class timer completed!', 'warning');
            }
        } else if (progressPercent >= 75) {
            progressBar.style.background = 'linear-gradient(90deg, #FFA500, #FFD700)';
        } else if (progressPercent >= 50) {
            progressBar.style.background = 'linear-gradient(90deg, #3B82F6, #1D4ED8)';
        } else {
            progressBar.style.background = 'linear-gradient(90deg, #10B981, #059669)';
        }
    }
}

/**
 * Add score to athlete (using current settings)
 */
function addScore(athleteId, buttonType) {
    const card = document.querySelector(`[data-athlete-id="${athleteId}"]`);
    if (card && card.classList.contains('completed')) return;

    // Get points from settings based on button type
    let points;
    switch (buttonType) {
        case 1: points = currentSettings.smallPoints; break;
        case 2: points = currentSettings.mediumPoints; break;
        case 5: points = currentSettings.largePoints; break;
        case 10: points = currentSettings.megaPoints; break;
        default: points = buttonType; // Fallback for direct point values
    }

    athleteScores[athleteId] = (athleteScores[athleteId] || 0) + points;
    updateScoreDisplay(athleteId);

    // Visual feedback
    showScoreAnimation(athleteId, `+${points}`);
}

/**
 * Add mystery score (using enhanced settings) - FIXED VERSION
 */
function addMysteryScore(athleteId) {
    const card = document.querySelector(`[data-athlete-id="${athleteId}"]`);
    if (card && card.classList.contains('completed')) return;

    const currentScore = athleteScores[athleteId] || 0;
    let mysteryResult;
    let animationText;
    let notificationText;
    let notificationType = 'info';
    let finalScore = currentScore;

    if (currentSettings.mysteryMode === 'range') {
        // Enhanced mode with negative/positive/reset outcomes

        // Check for reset outcome first
        const resetChance = currentSettings.mysteryResetChance || 10;
        if (Math.random() * 100 < resetChance) {
            // FIXED: Reset outcome - properly track the change
            finalScore = 0;
            mysteryResult = -currentScore; // Track the actual change for logging
            animationText = '🔄 RESET';
            notificationText = 'Mystery reset! Session score set to 0';
            notificationType = 'warning';
            console.log(`Mystery reset for athlete ${athleteId}: ${currentScore} → 0`);
        } else {
            // Regular outcome from range
            const outcomes = currentSettings.mysteryOutcomes || [-20, -15, -10, -5, 0, 5, 10, 15, 20];
            const randomOutcome = outcomes[Math.floor(Math.random() * outcomes.length)];

            mysteryResult = randomOutcome;
            finalScore = currentScore + mysteryResult;

            // FIXED: Properly handle negative scores with validation
            if (!currentSettings.allowNegative && finalScore < 0) {
                const actualChange = -currentScore; // Only subtract what's available
                finalScore = 0;
                mysteryResult = actualChange; // Update to reflect actual change
                console.log(`Mystery score clamped to 0 for athlete ${athleteId}: ${currentScore} + ${randomOutcome} → 0`);
            }

            // Visual feedback based on outcome
            if (mysteryResult > 0) {
                animationText = `+${mysteryResult} ✨`;
                notificationText = `Mystery bonus: +${mysteryResult} points!`;
                notificationType = 'success';
            } else if (mysteryResult < 0) {
                animationText = `${mysteryResult} 💥`;
                notificationText = `Mystery penalty: ${mysteryResult} points!`;
                notificationType = 'error';
            } else {
                animationText = `${mysteryResult} 😐`;
                notificationText = `Mystery result: No change`;
                notificationType = 'info';
            }
        }
    } else {
        // Classic mode - simple min/max range
        const min = currentSettings.mysteryMin || 1;
        const max = currentSettings.mysteryMax || 10;
        mysteryResult = Math.floor(Math.random() * (max - min + 1)) + min;

        finalScore = currentScore + mysteryResult;

        // FIXED: Apply negative score validation in classic mode too
        if (!currentSettings.allowNegative && finalScore < 0) {
            finalScore = 0;
            mysteryResult = -currentScore;
        }

        animationText = `+${mysteryResult} ✨`;
        notificationText = `Mystery bonus: +${mysteryResult} points!`;
        notificationType = 'success';
    }

    // FIXED: Atomic update with validation and logging
    athleteScores[athleteId] = Math.max(0, finalScore); // Extra safety check
    console.log(`Mystery score for athlete ${athleteId}: ${currentScore} → ${athleteScores[athleteId]} (change: ${mysteryResult})`);

    updateScoreDisplay(athleteId);
    showScoreAnimation(athleteId, animationText);
    showNotification(notificationText, notificationType);
}

/**
 * Add miss (using settings penalty)
 */
function addMiss(athleteId) {
    const card = document.querySelector(`[data-athlete-id="${athleteId}"]`);
    if (card && card.classList.contains('completed')) return;

    const penalty = currentSettings.missPenalty;
    const currentScore = athleteScores[athleteId] || 0;

    // Apply penalty, respecting negative score setting
    if (currentSettings.allowNegative) {
        athleteScores[athleteId] = currentScore - penalty;
    } else {
        athleteScores[athleteId] = Math.max(0, currentScore - penalty);
    }

    updateScoreDisplay(athleteId);

    // Visual feedback for miss
    showScoreAnimation(athleteId, `-${penalty} ❌`);
    showNotification(`Missed target! -${penalty} points`, 'error');
}

/**
 * Update score display
 */
function updateScoreDisplay(athleteId) {
    const scoreDisplay = document.getElementById(`current-score-${athleteId}`);
    if (scoreDisplay) {
        scoreDisplay.textContent = athleteScores[athleteId] || 0;

        // Add pulse animation
        scoreDisplay.style.animation = 'none';
        setTimeout(() => {
            scoreDisplay.style.animation = 'pulse 0.5s ease-in-out';
        }, 10);
    }
}

/**
 * Show score animation
 */
function showScoreAnimation(athleteId, text) {
    const card = document.querySelector(`[data-athlete-id="${athleteId}"]`);
    if (!card) return;

    const animation = document.createElement('div');
    animation.className = 'score-animation';
    animation.textContent = text;
    card.appendChild(animation);

    setTimeout(() => {
        if (animation.parentNode) {
            animation.parentNode.removeChild(animation);
        }
    }, 1500);
}

/**
 * Get athlete name by ID
 */
function getAthleteName(athleteId) {
    const athlete = currentRunAthletes.find(a => a.id === athleteId);
    return athlete ? athlete.name : 'Athlete';
}

/**
 * Submit run for individual athlete
 */
async function submitAthleteRun(athleteId) {
    const notesInput = document.getElementById(`notes-${athleteId}`);
    const card = document.querySelector(`[data-athlete-id="${athleteId}"]`);
    const submitBtn = document.getElementById(`submit-btn-${athleteId}`);

    if (!notesInput || !card) return;

    const score = athleteScores[athleteId] || 0;
    const notes = notesInput.value.trim();
    const classTimeValue = classTimer.elapsedTime;

    // FIXED: Create run session entry with unique IDs
    const runEntry = {
        sessionId: generateUniqueSessionId(currentRunClass.id),
        id: generateUniqueRunId(),
        athleteId: athleteId,
        classId: currentRunClass.id,
        score: Math.max(0, score), // FIXED: Ensure score is never negative
        classTime: Math.round(classTimeValue * 100) / 100, // Class timer when submitted
        notes: notes || '', // FIXED: Ensure notes is never null
        date: new Date().toISOString(),
        sessionDate: sessionStartTime ? sessionStartTime.toISOString() : new Date().toISOString(),
        submittedAt: new Date().toISOString()
    };

    try {
        // Save to run sessions
        await saveRunSession(runEntry);

        // Update UI - mark as completed
        card.classList.add('completed');
        submitBtn.innerHTML = '<i class="fas fa-check-circle"></i> Complete';
        submitBtn.disabled = true;
        notesInput.disabled = true;

        // Disable scoring buttons for this athlete
        const scoringButtons = card.querySelectorAll('.score-btn');
        scoringButtons.forEach(btn => btn.disabled = true);

        const athlete = currentRunAthletes.find(a => a.id === athleteId);
        const classMinutes = Math.floor(classTimeValue / 60);
        const classSeconds = Math.floor(classTimeValue % 60);
        showNotification(`Run completed for ${athlete ? athlete.name : 'athlete'}! Score: ${score}, Class Time: ${classMinutes}:${classSeconds.toString().padStart(2, '0')}`, 'success');

        // Check for achievement unlocks
        await checkAchievements(athleteId);

    } catch (error) {
        console.error('Error submitting athlete run:', error);
        showNotification('Failed to submit run', 'error');
    }
}

/**
 * Save all scores at once
 */
async function saveAllScores() {
    const trackingCards = document.querySelectorAll('.athlete-tracking-card');
    let savedCount = 0;
    let errors = 0;

    for (const card of trackingCards) {
        if (card.classList.contains('completed')) continue; // Skip already completed athletes

        const athleteId = card.dataset.athleteId;
        const currentScore = athleteScores[athleteId] || 0;

        // Only save if athlete has a score greater than 0
        if (currentScore > 0) {
            try {
                await submitAthleteRun(athleteId);
                savedCount++;
            } catch (error) {
                console.error(`Error saving score for athlete ${athleteId}:`, error);
                errors++;
            }
        }
    }

    if (savedCount > 0) {
        showNotification(`Saved ${savedCount} scores successfully!`, 'success');
    }
    if (errors > 0) {
        showNotification(`${errors} scores failed to save`, 'error');
    }
    if (savedCount === 0 && errors === 0) {
        showNotification('No scores to save - add some points first!', 'info');
    }
}

/**
 * Clear all scores and notes
 */
function clearAllScores() {
    if (!confirm('Are you sure you want to clear all scores and notes? This cannot be undone.')) {
        return;
    }

    const trackingCards = document.querySelectorAll('.athlete-tracking-card');

    trackingCards.forEach(card => {
        const athleteId = card.dataset.athleteId;

        // Reset score
        athleteScores[athleteId] = 0;
        updateScoreDisplay(athleteId);

        // Reset controls
        const notesInput = document.getElementById(`notes-${athleteId}`);
        const submitBtn = document.getElementById(`submit-btn-${athleteId}`);
        const scoringButtons = card.querySelectorAll('.score-btn');

        if (notesInput) {
            notesInput.value = '';
            notesInput.disabled = false;
        }
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-check"></i> Submit Run';
            submitBtn.disabled = false;
        }
        scoringButtons.forEach(btn => btn.disabled = false);

        card.classList.remove('completed');
    });

    showNotification('All scores and notes cleared! 🧹', 'success');
}

/**
 * Load existing run sessions
 */
async function loadRunSessions() {
    try {
        runSessions = await ipcRenderer.invoke('load-run-sessions') || [];
        console.log(`Loaded ${runSessions.length} run sessions`);
    } catch (error) {
        console.error('Error loading run sessions:', error);
        runSessions = [];
    }
}

/**
 * Save a run session entry - FIXED VERSION (prevents race conditions)
 */
async function saveRunSession(runEntry) {
    try {
        // FIXED: Load fresh data from storage to prevent race conditions
        const currentRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];

        // FIXED: Validate run entry before saving
        if (!runEntry.athleteId || !runEntry.classId || typeof runEntry.score !== 'number') {
            throw new Error('Invalid run entry data');
        }

        // FIXED: Check for duplicate entries
        const isDuplicate = currentRunSessions.some(session =>
            session.id === runEntry.id ||
            (session.athleteId === runEntry.athleteId &&
             session.classId === runEntry.classId &&
             Math.abs(new Date(session.submittedAt) - new Date(runEntry.submittedAt)) < 1000)
        );

        if (isDuplicate) {
            console.warn('Duplicate run session detected, skipping save');
            return true; // Don't throw error, just skip
        }

        // FIXED: Add to fresh data and save atomically
        currentRunSessions.push(runEntry);
        const result = await ipcRenderer.invoke('save-run-sessions', currentRunSessions);

        if (!result.success) {
            throw new Error('Failed to save run session to storage');
        }

        // FIXED: Update local cache only after successful save
        runSessions = currentRunSessions;

        console.log(`Run session saved successfully for athlete ${runEntry.athleteId}, score: ${runEntry.score}`);
        return true;

    } catch (error) {
        console.error('Error saving run session:', error);
        throw error;
    }
}

/**
 * Settings Page Functionality
 */

// Default scoring settings
const DEFAULT_SETTINGS = {
    smallPoints: 1,
    mediumPoints: 2,
    largePoints: 5,
    megaPoints: 10,
    mysteryMin: 1,
    mysteryMax: 10,
    missPenalty: 1,
    allowNegative: true,
    speedBonusEnabled: false,
    speedThreshold: 30,
    speedMultiplier: 1.5,
    streakBonusEnabled: false,
    streakThreshold: 3,
    streakBonus: 5,
    // Enhanced Mystery Button Settings
    mysteryMode: 'range', // 'range' or 'classic'
    mysteryRangeMin: -20,
    mysteryRangeMax: 20,
    mysteryResetChance: 10, // Percentage chance for reset outcome
    mysteryOutcomes: [-20, -15, -10, -5, 0, 5, 10, 15, 20] // Possible outcomes
};

// Current settings (loaded from storage or defaults)
let currentSettings = { ...DEFAULT_SETTINGS };

/**
 * Set up settings page functionality
 */
function setupSettingsPage() {
    // Load settings when page loads
    loadSettings();

    // Set up event listeners
    setupSettingsEventListeners();

    // Set up accordion behavior
    setupSettingsAccordion();
}

/**
 * Set up event listeners for settings controls
 */
function setupSettingsEventListeners() {
    // Save settings button
    const saveBtn = document.getElementById('save-settings-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', saveSettings);
    }

    // Reset settings button
    const resetBtn = document.getElementById('reset-settings-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetSettings);
    }

    // Export settings button
    const exportBtn = document.getElementById('export-settings-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportSettings);
    }

    // Preview buttons
    const previewTimerBtn = document.getElementById('preview-timer-sound');
    if (previewTimerBtn) {
        previewTimerBtn.addEventListener('click', previewTimerSound);
    }

    const previewPointBtn = document.getElementById('preview-point-sound');
    if (previewPointBtn) {
        previewPointBtn.addEventListener('click', previewPointSound);
    }

    const previewThemeBtn = document.getElementById('preview-theme');
    if (previewThemeBtn) {
        previewThemeBtn.addEventListener('click', previewTheme);
    }

    // Achievement buttons
    const viewAchievementsBtn = document.getElementById('view-achievements-btn');
    if (viewAchievementsBtn) {
        viewAchievementsBtn.addEventListener('click', () => {
            navigateToPage('achievements-page');
        });
    }

    const resetAchievementsBtn = document.getElementById('reset-achievements-btn');
    if (resetAchievementsBtn) {
        resetAchievementsBtn.addEventListener('click', resetAllAchievements);
    }

    // Mystery mode switching
    const mysteryModeSelect = document.getElementById('mystery-mode');
    if (mysteryModeSelect) {
        mysteryModeSelect.addEventListener('change', handleMysteryModeChange);
        // Initialize on page load
        handleMysteryModeChange();
    }

    // Mystery range inputs for auto-generating outcomes
    const mysteryRangeMin = document.getElementById('mystery-range-min');
    const mysteryRangeMax = document.getElementById('mystery-range-max');
    if (mysteryRangeMin && mysteryRangeMax) {
        mysteryRangeMin.addEventListener('input', updateMysteryOutcomes);
        mysteryRangeMax.addEventListener('input', updateMysteryOutcomes);
    }

    // Athlete management buttons
    const addAthleteBtn = document.getElementById('add-athlete-btn');
    if (addAthleteBtn) {
        addAthleteBtn.addEventListener('click', () => openAthleteModal());
    }

    const refreshAthletesBtn = document.getElementById('refresh-athletes-btn');
    if (refreshAthletesBtn) {
        refreshAthletesBtn.addEventListener('click', loadAthleteManagementList);
    }

    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', bulkDeleteAthletes);
    }

    // Athlete modal form
    const athleteModalForm = document.getElementById('athlete-modal-form');
    if (athleteModalForm) {
        athleteModalForm.addEventListener('submit', handleAthleteModalSubmit);
    }

    // Avatar upload handling
    const avatarInput = document.getElementById('athlete-modal-avatar');
    if (avatarInput) {
        avatarInput.addEventListener('change', handleAvatarUpload);
    }

    const removeAvatarBtn = document.getElementById('remove-avatar-btn');
    if (removeAvatarBtn) {
        removeAvatarBtn.addEventListener('click', removeAvatar);
    }

    // Load athlete management list when settings page loads
    loadAthleteManagementList();

    // Enable/disable dependent controls
    const speedBonusCheckbox = document.getElementById('speed-bonus-enabled');
    if (speedBonusCheckbox) {
        speedBonusCheckbox.addEventListener('change', toggleSpeedBonusControls);
    }

    const streakBonusCheckbox = document.getElementById('streak-bonus-enabled');
    if (streakBonusCheckbox) {
        streakBonusCheckbox.addEventListener('change', toggleStreakBonusControls);
    }
}

/**
 * Load settings from storage or use defaults
 */
async function loadSettings() {
    try {
        const savedSettings = await ipcRenderer.invoke('load-settings');
        if (savedSettings) {
            currentSettings = { ...DEFAULT_SETTINGS, ...savedSettings };
        }

        // Apply settings to UI
        applySettingsToUI();

        console.log('✅ Settings loaded:', currentSettings);
    } catch (error) {
        console.error('❌ Error loading settings:', error);
        // Use defaults if loading fails
        applySettingsToUI();
    }
}

/**
 * Apply current settings to UI controls
 */
function applySettingsToUI() {
    // Basic point values
    setInputValue('small-points', currentSettings.smallPoints);
    setInputValue('medium-points', currentSettings.mediumPoints);
    setInputValue('large-points', currentSettings.largePoints);
    setInputValue('mega-points', currentSettings.megaPoints);

    // Mystery button settings
    setSelectValue('mystery-mode', currentSettings.mysteryMode || 'classic');
    setInputValue('mystery-min', currentSettings.mysteryMin);
    setInputValue('mystery-max', currentSettings.mysteryMax);
    setInputValue('mystery-range-min', currentSettings.mysteryRangeMin || -20);
    setInputValue('mystery-range-max', currentSettings.mysteryRangeMax || 20);
    setInputValue('mystery-reset-chance', currentSettings.mysteryResetChance || 10);
    setInputValue('mystery-outcomes', (currentSettings.mysteryOutcomes || [-20, -15, -10, -5, 0, 5, 10, 15, 20]).join(','));

    // Other special scoring
    setInputValue('miss-penalty', currentSettings.missPenalty);
    setCheckboxValue('allow-negative', currentSettings.allowNegative);

    // Handle mystery mode display
    handleMysteryModeChange();

    // Speed bonuses
    setCheckboxValue('speed-bonus-enabled', currentSettings.speedBonusEnabled);
    setInputValue('speed-threshold', currentSettings.speedThreshold);
    setSelectValue('speed-multiplier', currentSettings.speedMultiplier);

    // Streak bonuses
    setCheckboxValue('streak-bonus-enabled', currentSettings.streakBonusEnabled);
    setInputValue('streak-threshold', currentSettings.streakThreshold);
    setInputValue('streak-bonus', currentSettings.streakBonus);

    // Update dependent control states
    toggleSpeedBonusControls();
    toggleStreakBonusControls();
}

/**
 * Helper function to set input value safely
 */
function setInputValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.value = value;
    }
}

/**
 * Helper function to set checkbox value safely
 */
function setCheckboxValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.checked = value;
    }
}

/**
 * Helper function to set select value safely
 */
function setSelectValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.value = value;
    }
}

/**
 * Toggle speed bonus controls based on checkbox
 */
function toggleSpeedBonusControls() {
    const enabled = document.getElementById('speed-bonus-enabled')?.checked || false;
    const threshold = document.getElementById('speed-threshold');
    const multiplier = document.getElementById('speed-multiplier');

    if (threshold) threshold.disabled = !enabled;
    if (multiplier) multiplier.disabled = !enabled;
}

/**
 * Toggle streak bonus controls based on checkbox
 */
function toggleStreakBonusControls() {
    const enabled = document.getElementById('streak-bonus-enabled')?.checked || false;
    const threshold = document.getElementById('streak-threshold');
    const bonus = document.getElementById('streak-bonus');

    if (threshold) threshold.disabled = !enabled;
    if (bonus) bonus.disabled = !enabled;
}

/**
 * Save current settings
 */
async function saveSettings() {
    try {
        // Collect settings from UI
        const newSettings = {
            smallPoints: parseInt(document.getElementById('small-points')?.value) || DEFAULT_SETTINGS.smallPoints,
            mediumPoints: parseInt(document.getElementById('medium-points')?.value) || DEFAULT_SETTINGS.mediumPoints,
            largePoints: parseInt(document.getElementById('large-points')?.value) || DEFAULT_SETTINGS.largePoints,
            megaPoints: parseInt(document.getElementById('mega-points')?.value) || DEFAULT_SETTINGS.megaPoints,

            // Enhanced mystery settings
            mysteryMode: document.getElementById('mystery-mode')?.value || DEFAULT_SETTINGS.mysteryMode,
            mysteryMin: parseInt(document.getElementById('mystery-min')?.value) || DEFAULT_SETTINGS.mysteryMin,
            mysteryMax: parseInt(document.getElementById('mystery-max')?.value) || DEFAULT_SETTINGS.mysteryMax,
            mysteryRangeMin: parseInt(document.getElementById('mystery-range-min')?.value) || DEFAULT_SETTINGS.mysteryRangeMin,
            mysteryRangeMax: parseInt(document.getElementById('mystery-range-max')?.value) || DEFAULT_SETTINGS.mysteryRangeMax,
            mysteryResetChance: parseInt(document.getElementById('mystery-reset-chance')?.value) || DEFAULT_SETTINGS.mysteryResetChance,
            mysteryOutcomes: document.getElementById('mystery-outcomes')?.value.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n)) || DEFAULT_SETTINGS.mysteryOutcomes,

            missPenalty: parseInt(document.getElementById('miss-penalty')?.value) || DEFAULT_SETTINGS.missPenalty,
            allowNegative: document.getElementById('allow-negative')?.checked || DEFAULT_SETTINGS.allowNegative,
            speedBonusEnabled: document.getElementById('speed-bonus-enabled')?.checked || DEFAULT_SETTINGS.speedBonusEnabled,
            speedThreshold: parseInt(document.getElementById('speed-threshold')?.value) || DEFAULT_SETTINGS.speedThreshold,
            speedMultiplier: parseFloat(document.getElementById('speed-multiplier')?.value) || DEFAULT_SETTINGS.speedMultiplier,
            streakBonusEnabled: document.getElementById('streak-bonus-enabled')?.checked || DEFAULT_SETTINGS.streakBonusEnabled,
            streakThreshold: parseInt(document.getElementById('streak-threshold')?.value) || DEFAULT_SETTINGS.streakThreshold,
            streakBonus: parseInt(document.getElementById('streak-bonus')?.value) || DEFAULT_SETTINGS.streakBonus
        };

        // Validate settings
        if (newSettings.mysteryMin > newSettings.mysteryMax) {
            showNotification('Mystery minimum cannot be greater than maximum!', 'error');
            return;
        }

        // Save to storage
        const result = await ipcRenderer.invoke('save-settings', newSettings);

        if (result.success) {
            currentSettings = newSettings;
            showNotification('Settings saved successfully! 🎮', 'success');
            console.log('✅ Settings saved:', currentSettings);
        } else {
            showNotification('Failed to save settings', 'error');
        }

    } catch (error) {
        console.error('❌ Error saving settings:', error);
        showNotification('Error saving settings', 'error');
    }
}

/**
 * Reset settings to defaults
 */
async function resetSettings() {
    if (!confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
        return;
    }

    try {
        currentSettings = { ...DEFAULT_SETTINGS };
        applySettingsToUI();

        // Save defaults to storage
        const result = await ipcRenderer.invoke('save-settings', currentSettings);

        if (result.success) {
            showNotification('Settings reset to defaults! 🔄', 'success');
            console.log('✅ Settings reset to defaults');
        } else {
            showNotification('Failed to reset settings', 'error');
        }

    } catch (error) {
        console.error('❌ Error resetting settings:', error);
        showNotification('Error resetting settings', 'error');
    }
}

/**
 * Export settings as JSON file
 */
function exportSettings() {
    try {
        const settingsData = {
            exportDate: new Date().toISOString(),
            appVersion: '1.0.0',
            settings: currentSettings
        };

        const dataStr = JSON.stringify(settingsData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `ninja-warrior-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showNotification('Settings exported successfully! 📁', 'success');

    } catch (error) {
        console.error('❌ Error exporting settings:', error);
        showNotification('Error exporting settings', 'error');
    }
}

/**
 * Get current point value for scoring buttons
 */
function getPointValue(buttonType) {
    switch (buttonType) {
        case 'small': return currentSettings.smallPoints;
        case 'medium': return currentSettings.mediumPoints;
        case 'large': return currentSettings.largePoints;
        case 'mega': return currentSettings.megaPoints;
        default: return 1;
    }
}

/**
 * Get mystery point range
 */
function getMysteryPointRange() {
    return {
        min: currentSettings.mysteryMin,
        max: currentSettings.mysteryMax
    };
}

/**
 * Get miss penalty value
 */
function getMissPenalty() {
    return currentSettings.missPenalty;
}

/**
 * Check if negative scores are allowed
 */
function areNegativeScoresAllowed() {
    return currentSettings.allowNegative;
}

/**
 * Set up accordion behavior for settings sections
 */
function setupSettingsAccordion() {
    const settingsItems = document.querySelectorAll('.settings-item');

    settingsItems.forEach(item => {
        const header = item.querySelector('.settings-item-header');
        if (header) {
            header.addEventListener('click', () => {
                // Close all other sections
                settingsItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current section
                item.classList.toggle('active');
            });
        }
    });
}

/**
 * Navigate to a specific page (for settings links)
 */
function navigateToPage(pageId) {
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        targetPage.classList.add('active');

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const targetNavLink = document.querySelector(`[data-page="${pageId}"]`);
        if (targetNavLink) {
            targetNavLink.classList.add('active');
        }
    }
}

/**
 * Preview timer sound
 */
function previewTimerSound() {
    const soundType = document.getElementById('timer-sound-type')?.value || 'bell';
    const volume = document.getElementById('timer-volume')?.value || 50;

    // Create audio context for preview
    try {
        // For now, show notification - actual sound implementation would go here
        showNotification(`🔊 Playing ${soundType} sound at ${volume}% volume`, 'info');
        console.log(`Preview timer sound: ${soundType} at ${volume}% volume`);
    } catch (error) {
        console.error('Error previewing timer sound:', error);
        showNotification('Error playing sound preview', 'error');
    }
}

/**
 * Preview point sound
 */
function previewPointSound() {
    const soundType = document.getElementById('point-sound-type')?.value || 'coin';

    try {
        // For now, show notification - actual sound implementation would go here
        showNotification(`🎵 Playing ${soundType} point sound`, 'info');
        console.log(`Preview point sound: ${soundType}`);
    } catch (error) {
        console.error('Error previewing point sound:', error);
        showNotification('Error playing sound preview', 'error');
    }
}

/**
 * Preview theme changes
 */
function previewTheme() {
    const primaryColor = document.getElementById('primary-color')?.value || '#FF0000';
    const secondaryColor = document.getElementById('secondary-color')?.value || '#0033FF';

    try {
        // Apply temporary theme preview
        document.documentElement.style.setProperty('--preview-primary', primaryColor);
        document.documentElement.style.setProperty('--preview-secondary', secondaryColor);

        showNotification(`🎨 Theme preview applied! Primary: ${primaryColor}, Secondary: ${secondaryColor}`, 'info');
        console.log(`Preview theme: Primary ${primaryColor}, Secondary ${secondaryColor}`);

        // Reset preview after 5 seconds
        setTimeout(() => {
            document.documentElement.style.removeProperty('--preview-primary');
            document.documentElement.style.removeProperty('--preview-secondary');
            showNotification('Theme preview reset', 'info');
        }, 5000);

    } catch (error) {
        console.error('Error previewing theme:', error);
        showNotification('Error applying theme preview', 'error');
    }
}

/**
 * Achievement System Functions
 */

/**
 * Set up achievement system
 */
async function setupAchievementSystem() {
    try {
        // Load achievements and athlete progress
        await loadAchievements();
        await loadAthleteAchievements();

        // Set up achievement page if it exists
        setupAchievementPage();

        console.log('✅ Achievement system initialized');
    } catch (error) {
        console.error('❌ Error setting up achievement system:', error);
    }
}

/**
 * Load all achievements from storage
 */
async function loadAchievements() {
    try {
        allAchievements = await ipcRenderer.invoke('load-achievements') || [];
        console.log(`📋 Loaded ${allAchievements.length} achievements`);
    } catch (error) {
        console.error('Error loading achievements:', error);
        allAchievements = [];
    }
}

/**
 * Load athlete achievement progress from storage
 */
async function loadAthleteAchievements() {
    try {
        athleteAchievements = await ipcRenderer.invoke('load-athlete-achievements') || {};
        console.log('📊 Loaded athlete achievement progress');
    } catch (error) {
        console.error('Error loading athlete achievements:', error);
        athleteAchievements = {};
    }
}

/**
 * Set up achievement page functionality
 */
function setupAchievementPage() {
    // Set up category filtering
    const categoryBtns = document.querySelectorAll('.category-btn');
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Update active category
            categoryBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            // Filter achievements
            const category = btn.dataset.category;
            filterAchievements(category);
        });
    });

    // Load achievements when page becomes active
    const achievementsPage = document.getElementById('achievements-page');
    if (achievementsPage) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (achievementsPage.classList.contains('active')) {
                        renderAchievements();
                    }
                }
            });
        });
        observer.observe(achievementsPage, { attributes: true });
    }
}

/**
 * Render achievements on the achievements page
 */
async function renderAchievements() {
    const achievementsGrid = document.getElementById('achievements-grid');
    const totalUnlocked = document.getElementById('total-unlocked');
    const totalAvailable = document.getElementById('total-available');
    const completionPercentage = document.getElementById('completion-percentage');

    if (!achievementsGrid) return;

    try {
        // Get all athletes to calculate achievement progress
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];

        // Calculate achievement progress for all athletes
        let totalUnlockedCount = 0;
        const achievementProgress = {};

        allAthletes.forEach(athlete => {
            const athleteProgress = calculateAthleteAchievementProgress(athlete, allRunSessions);
            achievementProgress[athlete.id] = athleteProgress;

            // Count unlocked achievements
            Object.values(athleteProgress).forEach(progress => {
                if (progress.unlocked) {
                    totalUnlockedCount++;
                }
            });
        });

        // Update stats
        if (totalUnlocked) totalUnlocked.textContent = totalUnlockedCount;
        if (totalAvailable) totalAvailable.textContent = allAchievements.length;
        if (completionPercentage) {
            const percentage = allAchievements.length > 0 ? Math.round((totalUnlockedCount / (allAchievements.length * allAthletes.length)) * 100) : 0;
            completionPercentage.textContent = `${percentage}%`;
        }

        // Render achievement cards
        achievementsGrid.innerHTML = allAchievements.map(achievement => {
            // Calculate how many athletes have unlocked this achievement
            const unlockedCount = allAthletes.filter(athlete => {
                const progress = achievementProgress[athlete.id];
                return progress && progress[achievement.id] && progress[achievement.id].unlocked;
            }).length;

            return createAchievementCard(achievement, unlockedCount, allAthletes.length);
        }).join('');

    } catch (error) {
        console.error('Error rendering achievements:', error);
        achievementsGrid.innerHTML = '<div class="error-message">Failed to load achievements</div>';
    }
}

/**
 * Calculate achievement progress for a specific athlete
 */
function calculateAthleteAchievementProgress(athlete, allRunSessions) {
    const progress = {};

    // Get athlete's run sessions
    const athleteRuns = allRunSessions.filter(run => run.athleteId === athlete.id);
    const totalScore = athleteRuns.reduce((sum, run) => sum + (run.score || 0), 0);

    allAchievements.forEach(achievement => {
        const req = achievement.requirement;
        let currentValue = 0;
        let isUnlocked = false;

        switch (req.type) {
            case 'total_points':
                currentValue = totalScore;
                isUnlocked = currentValue >= req.value;
                break;

            case 'runs_per_session':
                // Check if athlete has 5+ runs in any single session
                const sessionCounts = {};
                athleteRuns.forEach(run => {
                    sessionCounts[run.sessionId] = (sessionCounts[run.sessionId] || 0) + 1;
                });
                currentValue = Math.max(...Object.values(sessionCounts), 0);
                isUnlocked = currentValue >= req.value;
                break;

            case 'session_points':
                // Check highest score in a single session
                const sessionScores = {};
                athleteRuns.forEach(run => {
                    sessionScores[run.sessionId] = (sessionScores[run.sessionId] || 0) + (run.score || 0);
                });
                currentValue = Math.max(...Object.values(sessionScores), 0);
                isUnlocked = currentValue >= req.value;
                break;

            case 'unique_sessions':
                const uniqueSessions = new Set(athleteRuns.map(run => run.sessionId));
                currentValue = uniqueSessions.size;
                isUnlocked = currentValue >= req.value;
                break;

            case 'mystery_bonuses':
                // Count mystery bonuses (this would need to be tracked separately)
                currentValue = 0; // Placeholder
                isUnlocked = false;
                break;

            case 'comeback':
                // Check for comeback scenarios (this would need special tracking)
                currentValue = 0; // Placeholder
                isUnlocked = false;
                break;

            case 'consecutive_sessions':
                // This would need date-based tracking
                currentValue = 0; // Placeholder
                isUnlocked = false;
                break;
        }

        progress[achievement.id] = {
            current: currentValue,
            required: req.value,
            unlocked: isUnlocked,
            progress: Math.min(currentValue / req.value, 1)
        };
    });

    return progress;
}

/**
 * Create achievement card HTML
 */
function createAchievementCard(achievement, unlockedCount, totalAthletes) {
    const progressPercentage = totalAthletes > 0 ? Math.round((unlockedCount / totalAthletes) * 100) : 0;
    const isPopular = progressPercentage > 50;

    return `
        <div class="achievement-card ${unlockedCount > 0 ? 'unlocked' : 'locked'}">
            <div class="achievement-header">
                <div class="achievement-icon ${achievement.rarity}">
                    <i class="${achievement.icon}"></i>
                </div>
                <div class="achievement-info">
                    <h3>${achievement.name}</h3>
                    <div class="achievement-rarity ${achievement.rarity}">${achievement.rarity.toUpperCase()}</div>
                </div>
            </div>

            <div class="achievement-description">
                ${achievement.description}
            </div>

            <div class="achievement-progress">
                <div class="achievement-progress-bar">
                    <div class="achievement-progress-fill" style="width: ${progressPercentage}%"></div>
                </div>
                <div class="achievement-progress-text">
                    ${unlockedCount} of ${totalAthletes} athletes (${progressPercentage}%)
                </div>
            </div>

            <div class="achievement-reward ${unlockedCount > 0 ? 'unlocked' : ''}">
                <i class="fas fa-gift"></i>
                <span>+${achievement.reward.xp} XP</span>
                ${achievement.reward.title ? `<span>• "${achievement.reward.title}"</span>` : ''}
            </div>
        </div>
    `;
}

/**
 * Filter achievements by category
 */
function filterAchievements(category) {
    const achievementCards = document.querySelectorAll('.achievement-card');

    achievementCards.forEach(card => {
        if (category === 'all') {
            card.style.display = 'block';
        } else {
            // This would need category data in the card
            card.style.display = 'block'; // For now, show all
        }
    });
}

/**
 * Check and unlock achievements for an athlete after scoring
 */
async function checkAchievements(athleteId) {
    try {
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];

        const athlete = allAthletes.find(a => a.id === athleteId);
        if (!athlete) return;

        const progress = calculateAthleteAchievementProgress(athlete, allRunSessions);
        const previousProgress = athleteAchievements[athleteId] || {};

        // Check for newly unlocked achievements
        const newlyUnlocked = [];
        Object.keys(progress).forEach(achievementId => {
            const current = progress[achievementId];
            const previous = previousProgress[achievementId];

            if (current.unlocked && (!previous || !previous.unlocked)) {
                const achievement = allAchievements.find(a => a.id === achievementId);
                if (achievement) {
                    newlyUnlocked.push(achievement);
                }
            }
        });

        // Update stored progress
        athleteAchievements[athleteId] = progress;
        await ipcRenderer.invoke('save-athlete-achievements', athleteAchievements);

        // Show achievement notifications
        newlyUnlocked.forEach(achievement => {
            showAchievementUnlock(athlete, achievement);
        });

    } catch (error) {
        console.error('Error checking achievements:', error);
    }
}

/**
 * Show achievement unlock notification
 */
function showAchievementUnlock(athlete, achievement) {
    // Create achievement notification
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    notification.innerHTML = `
        <div class="achievement-notification-content">
            <div class="achievement-notification-icon ${achievement.rarity}">
                <i class="${achievement.icon}"></i>
            </div>
            <div class="achievement-notification-text">
                <div class="achievement-notification-title">🏆 Achievement Unlocked!</div>
                <div class="achievement-notification-name">${achievement.name}</div>
                <div class="achievement-notification-athlete">${athlete.name}</div>
                <div class="achievement-notification-reward">+${achievement.reward.xp} XP</div>
            </div>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Hide and remove after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 500);
    }, 5000);

    // Also show regular notification
    showNotification(`🏆 ${athlete.name} unlocked "${achievement.name}"!`, 'success');
}

/**
 * Reset all achievement progress
 */
async function resetAllAchievements() {
    if (!confirm('Are you sure you want to reset ALL achievement progress? This cannot be undone.')) {
        return;
    }

    try {
        // Clear all athlete achievement progress
        athleteAchievements = {};
        await ipcRenderer.invoke('save-athlete-achievements', athleteAchievements);

        // Refresh achievements page if active
        const achievementsPage = document.getElementById('achievements-page');
        if (achievementsPage && achievementsPage.classList.contains('active')) {
            await renderAchievements();
        }

        showNotification('All achievement progress has been reset', 'success');

    } catch (error) {
        console.error('Error resetting achievements:', error);
        showNotification('Failed to reset achievement progress', 'error');
    }
}

/**
 * Handle mystery mode change
 */
function handleMysteryModeChange() {
    const mysteryModeSelect = document.getElementById('mystery-mode');
    const classicSettings = document.getElementById('classic-mystery-settings');
    const enhancedSettings = document.getElementById('enhanced-mystery-settings');

    if (!mysteryModeSelect || !classicSettings || !enhancedSettings) return;

    const mode = mysteryModeSelect.value;

    if (mode === 'classic') {
        classicSettings.style.display = 'block';
        enhancedSettings.style.display = 'none';
    } else {
        classicSettings.style.display = 'none';
        enhancedSettings.style.display = 'block';
        updateMysteryOutcomes(); // Update outcomes when switching to enhanced mode
    }
}

/**
 * Update mystery outcomes based on range
 */
function updateMysteryOutcomes() {
    const minInput = document.getElementById('mystery-range-min');
    const maxInput = document.getElementById('mystery-range-max');
    const outcomesInput = document.getElementById('mystery-outcomes');

    if (!minInput || !maxInput || !outcomesInput) return;

    const min = parseInt(minInput.value) || -20;
    const max = parseInt(maxInput.value) || 20;

    // Generate outcomes in steps of 5
    const outcomes = [];
    for (let i = min; i <= max; i += 5) {
        outcomes.push(i);
    }

    // Ensure 0 is included if it's within range
    if (min <= 0 && max >= 0 && !outcomes.includes(0)) {
        outcomes.push(0);
        outcomes.sort((a, b) => a - b);
    }

    outcomesInput.value = outcomes.join(',');
}

/**
 * Athlete Management Functions
 */

/**
 * Load athlete management list
 */
async function loadAthleteManagementList() {
    const athletesList = document.getElementById('athletes-management-list');
    if (!athletesList) return;

    try {
        // Use cached athletes or load fresh data
        let allAthletes = getCachedAthletes();
        if (allAthletes.length === 0) {
            allAthletes = await ipcRenderer.invoke('load-athletes') || [];
            cachedAthletes = allAthletes;
        }

        if (allAthletes.length === 0) {
            athletesList.innerHTML = `
                <div class="no-athletes">
                    <i class="fas fa-users"></i>
                    <p>No athletes found. Add your first athlete!</p>
                </div>
            `;
            return;
        }

        athletesList.innerHTML = allAthletes.map(athlete => `
            <div class="athlete-management-item">
                <div class="athlete-checkbox">
                    <input type="checkbox" class="athlete-select" data-athlete-id="${athlete.id}">
                </div>
                <div class="athlete-avatar-small">
                    ${athlete.avatar && athlete.avatar !== 'placeholder-avatar.png' ?
                        `<img src="${athlete.avatar}" alt="${athlete.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` :
                        `<span>${getAthleteInitials(athlete.name)}</span>`
                    }
                </div>
                <div class="athlete-details">
                    <h5>${escapeHtml(athlete.name)}</h5>
                    ${athlete.nickname ? `<span class="athlete-nickname">"${escapeHtml(athlete.nickname)}"</span>` : ''}
                    <div class="athlete-meta">
                        <span class="athlete-age">Age: ${athlete.age}</span>
                        <span class="athlete-level">Level: ${athlete.level || 'Beginner'}</span>
                    </div>
                </div>
                <div class="athlete-actions">
                    <button class="btn btn-outline btn-sm" onclick="editAthlete('${athlete.id}')">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="deleteAthlete('${athlete.id}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </div>
            </div>
        `).join('');

        // Update bulk delete button state
        updateBulkDeleteButton();

        // Add event listeners for checkboxes
        const checkboxes = athletesList.querySelectorAll('.athlete-select');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkDeleteButton);
        });

    } catch (error) {
        console.error('Error loading athlete management list:', error);
        athletesList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Failed to load athletes</p>
            </div>
        `;
    }
}

/**
 * Update bulk delete button state
 */
function updateBulkDeleteButton() {
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const checkboxes = document.querySelectorAll('.athlete-select:checked');

    if (bulkDeleteBtn) {
        bulkDeleteBtn.disabled = checkboxes.length === 0;
        bulkDeleteBtn.textContent = checkboxes.length > 0 ?
            `Delete Selected (${checkboxes.length})` :
            'Delete Selected';
    }
}

/**
 * Open athlete modal for adding or editing
 */
function openAthleteModal(athleteId = null) {
    const modal = document.getElementById('athlete-modal-overlay');
    const title = document.getElementById('athlete-modal-title');
    const form = document.getElementById('athlete-modal-form');
    const submitBtn = document.getElementById('athlete-modal-submit');

    if (!modal || !title || !form) return;

    // Reset form
    form.reset();
    document.getElementById('athlete-modal-id').value = athleteId || '';

    if (athleteId) {
        // Edit mode
        title.textContent = 'Edit Athlete';
        submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Athlete';
        loadAthleteForEdit(athleteId);
    } else {
        // Add mode
        title.textContent = 'Add New Athlete';
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Athlete';
        resetAvatarPreview();
    }

    modal.style.display = 'flex';
}

/**
 * Close athlete modal
 */
function closeAthleteModal() {
    const modal = document.getElementById('athlete-modal-overlay');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Load athlete data for editing
 */
async function loadAthleteForEdit(athleteId) {
    try {
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const athlete = allAthletes.find(a => a.id === athleteId);

        if (!athlete) {
            showNotification('Athlete not found', 'error');
            return;
        }

        // Populate form fields
        document.getElementById('athlete-modal-name').value = athlete.name || '';
        document.getElementById('athlete-modal-age').value = athlete.age || '';
        document.getElementById('athlete-modal-nickname').value = athlete.nickname || '';
        document.getElementById('athlete-modal-level').value = athlete.level || 'beginner';
        document.getElementById('athlete-modal-description').value = athlete.description || '';

        // Handle avatar
        const avatarImg = document.getElementById('athlete-modal-avatar-img');
        const avatarInitials = document.getElementById('athlete-modal-avatar-initials');
        const removeBtn = document.getElementById('remove-avatar-btn');

        if (athlete.avatar && athlete.avatar !== 'placeholder-avatar.png') {
            avatarImg.src = athlete.avatar;
            avatarImg.style.display = 'block';
            avatarInitials.style.display = 'none';
            removeBtn.style.display = 'inline-block';
        } else {
            avatarImg.style.display = 'none';
            avatarInitials.style.display = 'block';
            avatarInitials.textContent = getAthleteInitials(athlete.name);
            removeBtn.style.display = 'none';
        }

    } catch (error) {
        console.error('Error loading athlete for edit:', error);
        showNotification('Failed to load athlete data', 'error');
    }
}

/**
 * Handle avatar upload
 */
function handleAvatarUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showNotification('Please select a valid image file', 'error');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('Image file must be smaller than 5MB', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const avatarImg = document.getElementById('athlete-modal-avatar-img');
        const avatarInitials = document.getElementById('athlete-modal-avatar-initials');
        const removeBtn = document.getElementById('remove-avatar-btn');

        avatarImg.src = e.target.result;
        avatarImg.style.display = 'block';
        avatarInitials.style.display = 'none';
        removeBtn.style.display = 'inline-block';
    };
    reader.readAsDataURL(file);
}

/**
 * Remove avatar
 */
function removeAvatar() {
    const avatarInput = document.getElementById('athlete-modal-avatar');
    const avatarImg = document.getElementById('athlete-modal-avatar-img');
    const avatarInitials = document.getElementById('athlete-modal-avatar-initials');
    const removeBtn = document.getElementById('remove-avatar-btn');

    avatarInput.value = '';
    avatarImg.style.display = 'none';
    avatarInitials.style.display = 'block';
    removeBtn.style.display = 'none';

    // Update initials based on current name
    const nameInput = document.getElementById('athlete-modal-name');
    if (nameInput.value) {
        avatarInitials.textContent = getAthleteInitials(nameInput.value);
    } else {
        avatarInitials.textContent = '?';
    }
}

/**
 * Reset avatar preview
 */
function resetAvatarPreview() {
    const avatarImg = document.getElementById('athlete-modal-avatar-img');
    const avatarInitials = document.getElementById('athlete-modal-avatar-initials');
    const removeBtn = document.getElementById('remove-avatar-btn');

    avatarImg.style.display = 'none';
    avatarInitials.style.display = 'block';
    avatarInitials.textContent = '?';
    removeBtn.style.display = 'none';
}

/**
 * Handle athlete modal form submission
 */
async function handleAthleteModalSubmit(event) {
    event.preventDefault();

    const athleteId = document.getElementById('athlete-modal-id').value;
    const name = document.getElementById('athlete-modal-name').value.trim();
    const age = parseInt(document.getElementById('athlete-modal-age').value);
    const nickname = document.getElementById('athlete-modal-nickname').value.trim();
    const level = document.getElementById('athlete-modal-level').value;
    const description = document.getElementById('athlete-modal-description').value.trim();
    const avatarInput = document.getElementById('athlete-modal-avatar');

    if (!name || !age || !level) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    try {
        let avatarPath = null;

        // Handle avatar upload
        if (avatarInput.files && avatarInput.files[0]) {
            avatarPath = await saveAthleteAvatar(avatarInput.files[0], name);
        } else if (athleteId) {
            // Keep existing avatar for edits
            const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
            const existingAthlete = allAthletes.find(a => a.id === athleteId);
            if (existingAthlete) {
                avatarPath = existingAthlete.avatar;
            }
        }

        const athleteData = {
            id: athleteId || generateId(),
            name,
            age,
            nickname: nickname || null,
            level,
            description: description || null,
            avatar: avatarPath || 'placeholder-avatar.png',
            classIds: athleteId ? undefined : [] // Don't reset class assignments for edits
        };

        // Save athlete
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];

        if (athleteId) {
            // Update existing athlete
            const index = allAthletes.findIndex(a => a.id === athleteId);
            if (index !== -1) {
                // Preserve existing classIds
                athleteData.classIds = allAthletes[index].classIds;
                allAthletes[index] = athleteData;
            }
        } else {
            // Add new athlete
            allAthletes.push(athleteData);
        }

        await ipcRenderer.invoke('save-athletes', allAthletes);

        showNotification(
            athleteId ? 'Athlete updated successfully!' : 'Athlete added successfully!',
            'success'
        );

        closeAthleteModal();
        loadAthleteManagementList();

        // Trigger global data refresh
        await refreshAthleteData();

    } catch (error) {
        console.error('Error saving athlete:', error);
        showNotification('Failed to save athlete', 'error');
    }
}

/**
 * Save athlete avatar to local storage
 */
async function saveAthleteAvatar(file, athleteName) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            // For now, we'll store as base64 data URL
            // In a production app, you'd save to a proper file system
            resolve(e.target.result);
        };
        reader.onerror = function() {
            reject(new Error('Failed to read avatar file'));
        };
        reader.readAsDataURL(file);
    });
}

/**
 * Edit athlete
 */
function editAthlete(athleteId) {
    openAthleteModal(athleteId);
}

/**
 * Delete single athlete
 */
async function deleteAthlete(athleteId) {
    try {
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const athlete = allAthletes.find(a => a.id === athleteId);

        if (!athlete) {
            showNotification('Athlete not found', 'error');
            return;
        }

        if (!confirm(`Are you sure you want to delete "${athlete.name}"? This action cannot be undone.`)) {
            return;
        }

        // Remove athlete from list
        const updatedAthletes = allAthletes.filter(a => a.id !== athleteId);
        await ipcRenderer.invoke('save-athletes', updatedAthletes);

        // Also remove from run sessions (optional - you might want to keep historical data)
        // const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];
        // const updatedSessions = allRunSessions.filter(session => session.athleteId !== athleteId);
        // await ipcRenderer.invoke('save-run-sessions', updatedSessions);

        showNotification(`${athlete.name} has been deleted`, 'success');
        loadAthleteManagementList();

        // Trigger global data refresh
        await refreshAthleteData();

    } catch (error) {
        console.error('Error deleting athlete:', error);
        showNotification('Failed to delete athlete', 'error');
    }
}

/**
 * Bulk delete athletes
 */
async function bulkDeleteAthletes() {
    const checkboxes = document.querySelectorAll('.athlete-select:checked');
    const athleteIds = Array.from(checkboxes).map(cb => cb.dataset.athleteId);

    if (athleteIds.length === 0) {
        showNotification('No athletes selected', 'warning');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${athleteIds.length} athlete(s)? This action cannot be undone.`)) {
        return;
    }

    try {
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const updatedAthletes = allAthletes.filter(a => !athleteIds.includes(a.id));

        await ipcRenderer.invoke('save-athletes', updatedAthletes);

        showNotification(`${athleteIds.length} athlete(s) deleted successfully`, 'success');
        loadAthleteManagementList();

        // Trigger global data refresh
        await refreshAthleteData();

    } catch (error) {
        console.error('Error bulk deleting athletes:', error);
        showNotification('Failed to delete athletes', 'error');
    }
}

/**
 * Populate athlete profile page with data
 */
async function populateAthleteProfile(athlete, athleteRuns) {
    // Basic info
    const profileName = document.getElementById('profile-name');
    const profileNickname = document.getElementById('profile-nickname');
    const profileAvatar = document.getElementById('profile-avatar');
    const profileAvatarText = document.getElementById('profile-avatar-text');
    const profileLevel = document.getElementById('profile-level');
    const profileTitle = document.getElementById('profile-title');

    if (profileName) profileName.textContent = athlete.name;
    if (profileNickname) {
        if (athlete.nickname) {
            profileNickname.textContent = `"${athlete.nickname}"`;
            profileNickname.style.display = 'block';
        } else {
            profileNickname.style.display = 'none';
        }
    }
    // Handle avatar display
    if (profileAvatar && profileAvatarText) {
        if (athlete.avatar && athlete.avatar !== 'placeholder-avatar.png') {
            // Show image avatar
            profileAvatar.style.backgroundImage = `url(${athlete.avatar})`;
            profileAvatar.style.backgroundSize = 'cover';
            profileAvatar.style.backgroundPosition = 'center';
            profileAvatarText.style.display = 'none';
        } else {
            // Show initials
            profileAvatar.style.backgroundImage = 'none';
            profileAvatarText.style.display = 'flex';
            profileAvatarText.textContent = getAthleteInitials(athlete.name);
        }
    }

    // Calculate stats
    const totalPoints = athleteRuns.reduce((sum, run) => sum + (run.score || 0), 0);
    const sessionsCount = new Set(athleteRuns.map(run => run.sessionId)).size;
    const bestScore = Math.max(...athleteRuns.map(run => run.score || 0), 0);

    // Calculate level and XP
    const level = Math.floor(totalPoints / 100) + 1;
    const currentLevelXP = totalPoints % 100;
    const nextLevelXP = 100;

    // Update level display
    if (profileLevel) {
        profileLevel.innerHTML = `<i class="fas fa-star"></i><span>Level ${level}</span>`;
    }

    // Update XP bar
    const xpProgress = document.getElementById('profile-xp-progress');
    const xpText = document.getElementById('profile-xp-text');
    if (xpProgress) xpProgress.style.width = `${(currentLevelXP / nextLevelXP) * 100}%`;
    if (xpText) xpText.textContent = `${currentLevelXP} / ${nextLevelXP} XP`;

    // Update stats
    const totalPointsEl = document.getElementById('profile-total-points');
    const sessionsEl = document.getElementById('profile-sessions');
    const bestScoreEl = document.getElementById('profile-best-score');
    const achievementsEl = document.getElementById('profile-achievements');

    if (totalPointsEl) totalPointsEl.textContent = totalPoints;
    if (sessionsEl) sessionsEl.textContent = sessionsCount;
    if (bestScoreEl) bestScoreEl.textContent = bestScore;

    // Calculate and display achievements
    const athleteProgress = calculateAthleteAchievementProgress(athlete, athleteRuns);
    const unlockedAchievements = Object.values(athleteProgress).filter(p => p.unlocked);

    if (achievementsEl) achievementsEl.textContent = unlockedAchievements.length;

    // Set title based on achievements or level
    let title = 'Ninja Rookie';
    if (level >= 10) title = 'Ninja Master';
    else if (level >= 5) title = 'Ninja Warrior';
    else if (level >= 3) title = 'Ninja Student';

    // Override with achievement titles if available
    const latestAchievement = allAchievements.find(achievement => {
        const progress = athleteProgress[achievement.id];
        return progress && progress.unlocked && achievement.reward.title;
    });
    if (latestAchievement) {
        title = latestAchievement.reward.title;
    }

    if (profileTitle) profileTitle.textContent = title;

    // Populate achievements showcase
    populateAchievementsShowcase(athleteProgress);

    // Populate recent activity
    populateRecentActivity(athleteRuns);
}

/**
 * Populate achievements showcase
 */
function populateAchievementsShowcase(athleteProgress) {
    const achievementsList = document.getElementById('profile-achievements-list');
    if (!achievementsList) return;

    const achievementBadges = allAchievements.map(achievement => {
        const progress = athleteProgress[achievement.id];
        const isUnlocked = progress && progress.unlocked;
        const progressPercent = progress ? Math.round(progress.progress * 100) : 0;

        return `
            <div class="achievement-badge ${isUnlocked ? 'unlocked' : 'locked'}">
                <div class="achievement-badge-icon ${achievement.rarity}">
                    <i class="${achievement.icon}"></i>
                </div>
                <div class="achievement-badge-info">
                    <h4>${achievement.name}</h4>
                    <p>${achievement.description}</p>
                    ${!isUnlocked ? `
                        <div class="achievement-progress-mini">
                            <div class="progress-bar-mini">
                                <div class="progress-fill-mini" style="width: ${progressPercent}%"></div>
                            </div>
                            <span class="progress-text-mini">${progressPercent}%</span>
                        </div>
                    ` : `
                        <div class="achievement-unlocked-badge">
                            <i class="fas fa-check"></i> Unlocked!
                        </div>
                    `}
                </div>
            </div>
        `;
    }).join('');

    achievementsList.innerHTML = achievementBadges;
}

/**
 * Populate recent activity
 */
function populateRecentActivity(athleteRuns) {
    const activityList = document.getElementById('profile-activity-list');
    if (!activityList) return;

    // Sort runs by date (most recent first)
    const recentRuns = [...athleteRuns]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 10); // Show last 10 runs

    if (recentRuns.length === 0) {
        activityList.innerHTML = `
            <div class="no-activity">
                <i class="fas fa-clock"></i>
                <p>No recent activity</p>
            </div>
        `;
        return;
    }

    const activityItems = recentRuns.map(run => {
        const date = new Date(run.timestamp);
        const timeAgo = getTimeAgo(date);

        return `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-play"></i>
                </div>
                <div class="activity-info">
                    <div class="activity-title">Completed Run</div>
                    <div class="activity-details">
                        <span class="activity-score">+${run.score} points</span>
                        ${run.notes ? `<span class="activity-notes">"${run.notes}"</span>` : ''}
                    </div>
                    <div class="activity-time">${timeAgo}</div>
                </div>
            </div>
        `;
    }).join('');

    activityList.innerHTML = activityItems;
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
}

/**
 * Global Data Refresh System
 */

/**
 * Register a callback to be called when athlete data changes
 */
function onAthleteDataChange(callback) {
    dataRefreshCallbacks.athletes.push(callback);
}

/**
 * Register a callback to be called when class data changes
 */
function onClassDataChange(callback) {
    dataRefreshCallbacks.classes.push(callback);
}

/**
 * Register a callback to be called when run session data changes
 */
function onRunSessionDataChange(callback) {
    dataRefreshCallbacks.runSessions.push(callback);
}

/**
 * Trigger refresh of all components that depend on athlete data - FIXED VERSION
 */
async function refreshAthleteData() {
    try {
        console.log('🔄 Refreshing athlete data across all components...');

        // FIXED: Clear all caches and force reload from storage
        cachedAthletes = [];

        // FIXED: Add retry logic for data loading
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                cachedAthletes = await ipcRenderer.invoke('load-athletes') || [];
                break; // Success, exit retry loop
            } catch (error) {
                retryCount++;
                console.warn(`Retry ${retryCount}/${maxRetries} loading athlete data:`, error);
                if (retryCount >= maxRetries) {
                    throw error;
                }
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
            }
        }

        // FIXED: Validate data before using it
        if (!Array.isArray(cachedAthletes)) {
            console.error('Invalid athlete data format, resetting to empty array');
            cachedAthletes = [];
        }

        // Call all registered callbacks with fresh data
        for (const callback of dataRefreshCallbacks.athletes) {
            try {
                await callback(cachedAthletes);
            } catch (error) {
                console.error('Error in athlete data refresh callback:', error);
            }
        }

        console.log(`✅ Athlete data refreshed successfully (${cachedAthletes.length} athletes)`);
    } catch (error) {
        console.error('❌ Error refreshing athlete data:', error);
        // FIXED: Ensure we have a valid array even on error
        cachedAthletes = [];
    }
}

/**
 * Trigger refresh of all components that depend on class data
 */
async function refreshClassData() {
    try {
        console.log('🔄 Refreshing class data across all components...');

        // Reload class data from storage
        currentClasses = await ipcRenderer.invoke('load-classes') || [];

        // Call all registered callbacks
        for (const callback of dataRefreshCallbacks.classes) {
            try {
                await callback(currentClasses);
            } catch (error) {
                console.error('Error in class data refresh callback:', error);
            }
        }

        console.log('✅ Class data refreshed successfully');
    } catch (error) {
        console.error('❌ Error refreshing class data:', error);
    }
}

/**
 * Trigger refresh of all components that depend on run session data
 */
async function refreshRunSessionData() {
    try {
        console.log('🔄 Refreshing run session data across all components...');

        // Call all registered callbacks
        for (const callback of dataRefreshCallbacks.runSessions) {
            try {
                await callback();
            } catch (error) {
                console.error('Error in run session data refresh callback:', error);
            }
        }

        console.log('✅ Run session data refreshed successfully');
    } catch (error) {
        console.error('❌ Error refreshing run session data:', error);
    }
}

/**
 * Get cached athlete data (faster than loading from storage each time)
 */
function getCachedAthletes() {
    return cachedAthletes;
}

/**
 * Get specific athlete from cache
 */
function getCachedAthlete(athleteId) {
    return cachedAthletes.find(athlete => athlete.id === athleteId);
}

/**
 * Set up data synchronization callbacks
 */
async function setupDataSynchronization() {
    console.log('🔗 Setting up data synchronization...');

    // Initialize cached data
    cachedAthletes = await ipcRenderer.invoke('load-athletes') || [];
    currentClasses = await ipcRenderer.invoke('load-classes') || [];

    // Register callbacks for home page class cards
    onAthleteDataChange(refreshHomePageClassCards);
    onClassDataChange(refreshHomePageClassCards);

    // Register callbacks for run page
    onAthleteDataChange(refreshRunPageAthletes);

    // Register callbacks for leaderboard
    onAthleteDataChange(refreshLeaderboard);
    onRunSessionDataChange(refreshLeaderboard);

    // Register callbacks for athlete management
    onAthleteDataChange(refreshAthleteManagement);

    // Register callbacks for roster modals
    onAthleteDataChange(refreshOpenRosterModals);

    console.log('✅ Data synchronization set up successfully');
}

/**
 * Refresh home page class cards when athlete data changes
 */
async function refreshHomePageClassCards(athletes = null) {
    const homePage = document.getElementById('home-page');
    if (!homePage || !homePage.classList.contains('active')) {
        return; // Only refresh if home page is active
    }

    console.log('🔄 Refreshing home page class cards...');

    try {
        // Use provided athletes or get from cache
        const allAthletes = athletes || getCachedAthletes();

        // Update class cards with correct athlete counts
        const classCards = document.querySelectorAll('.class-card');
        classCards.forEach(card => {
            const classId = card.dataset.classId;
            if (classId) {
                const classAthletes = allAthletes.filter(athlete =>
                    athlete.classIds && athlete.classIds.includes(classId)
                );

                const athleteCountElement = card.querySelector('.athlete-count');
                if (athleteCountElement) {
                    athleteCountElement.textContent = `${classAthletes.length} athlete${classAthletes.length !== 1 ? 's' : ''}`;
                }
            }
        });

        console.log('✅ Home page class cards refreshed');
    } catch (error) {
        console.error('❌ Error refreshing home page class cards:', error);
    }
}

/**
 * Refresh run page athletes when data changes
 */
async function refreshRunPageAthletes(athletes = null) {
    const runPage = document.getElementById('run-page');
    if (!runPage || !runPage.classList.contains('active')) {
        return; // Only refresh if run page is active
    }

    console.log('🔄 Refreshing run page athletes...');

    // If there's an active class selected, refresh the athlete list
    const currentClassSelect = document.getElementById('run-class-select');
    if (currentClassSelect && currentClassSelect.value) {
        // Re-load the selected class to refresh athlete list
        await loadSelectedClass();
    }

    console.log('✅ Run page athletes refreshed');
}

/**
 * Refresh leaderboard when data changes
 */
async function refreshLeaderboard(athletes = null) {
    const leaderboardPage = document.getElementById('leaderboard-page');
    if (!leaderboardPage || !leaderboardPage.classList.contains('active')) {
        return; // Only refresh if leaderboard page is active
    }

    console.log('🔄 Refreshing leaderboard...');
    await loadLeaderboard();
    console.log('✅ Leaderboard refreshed');
}

/**
 * Refresh athlete management when data changes
 */
async function refreshAthleteManagement(athletes = null) {
    const settingsPage = document.getElementById('settings-page');
    if (!settingsPage || !settingsPage.classList.contains('active')) {
        return; // Only refresh if settings page is active
    }

    console.log('🔄 Refreshing athlete management...');
    await loadAthleteManagementList();
    console.log('✅ Athlete management refreshed');
}

/**
 * Refresh any open roster modals when data changes
 */
async function refreshOpenRosterModals(athletes = null) {
    const openModals = document.querySelectorAll('.roster-modal-overlay');
    if (openModals.length === 0) {
        return; // No open modals to refresh
    }

    console.log('🔄 Refreshing open roster modals...');

    // Close existing modals and let user re-open them
    // This is simpler than trying to update the modal content in place
    openModals.forEach(modal => {
        modal.remove();
    });

    // Show notification that roster was updated
    if (openModals.length > 0) {
        showNotification('Roster updated - please reopen to see changes', 'info');
    }

    console.log('✅ Open roster modals refreshed');
}

/**
 * Error Recovery and Validation System
 */

/**
 * Validate and repair data consistency
 */
async function validateAndRepairData() {
    console.log('🔍 Validating data consistency...');

    try {
        // Load all data
        const allAthletes = await ipcRenderer.invoke('load-athletes') || [];
        const allClasses = await ipcRenderer.invoke('load-classes') || [];
        const allRunSessions = await ipcRenderer.invoke('load-run-sessions') || [];

        let repairsMade = 0;

        // Validate athlete class assignments
        const validClassIds = new Set(allClasses.map(c => c.id));
        const repairedAthletes = allAthletes.map(athlete => {
            if (athlete.classIds) {
                const originalLength = athlete.classIds.length;
                athlete.classIds = athlete.classIds.filter(classId => validClassIds.has(classId));
                if (athlete.classIds.length !== originalLength) {
                    repairsMade++;
                    console.log(`🔧 Repaired athlete ${athlete.name}: removed invalid class assignments`);
                }
            }
            return athlete;
        });

        // Validate run sessions
        const validAthleteIds = new Set(allAthletes.map(a => a.id));
        const repairedRunSessions = allRunSessions.filter(session => {
            if (!validAthleteIds.has(session.athleteId)) {
                repairsMade++;
                console.log(`🔧 Removed orphaned run session for deleted athlete`);
                return false;
            }
            return true;
        });

        // Save repaired data if needed
        if (repairsMade > 0) {
            await ipcRenderer.invoke('save-athletes', repairedAthletes);
            await ipcRenderer.invoke('save-run-sessions', repairedRunSessions);
            console.log(`✅ Data validation complete: ${repairsMade} repairs made`);

            // Refresh cached data
            cachedAthletes = repairedAthletes;
            await refreshAthleteData();
        } else {
            console.log('✅ Data validation complete: no repairs needed');
        }

        return { repairsMade, isValid: true };

    } catch (error) {
        console.error('❌ Error during data validation:', error);
        return { repairsMade: 0, isValid: false, error };
    }
}

/**
 * Force refresh all data from storage
 */
async function forceRefreshAllData() {
    console.log('🔄 Force refreshing all data from storage...');

    try {
        // Clear cache
        cachedAthletes = [];

        // Reload everything
        cachedAthletes = await ipcRenderer.invoke('load-athletes') || [];
        currentClasses = await ipcRenderer.invoke('load-classes') || [];

        // Trigger all refresh callbacks
        await refreshAthleteData();
        await refreshClassData();

        console.log('✅ Force refresh complete');
        showNotification('Data refreshed successfully', 'success');

    } catch (error) {
        console.error('❌ Error during force refresh:', error);
        showNotification('Failed to refresh data', 'error');
    }
}

/**
 * Check if data is synchronized across components
 */
function checkDataSynchronization() {
    const issues = [];

    // Check if cached data exists
    if (cachedAthletes.length === 0) {
        issues.push('No cached athlete data');
    }

    // Check if home page shows correct counts
    const classCards = document.querySelectorAll('.class-card');
    if (classCards.length === 0 && currentClasses.length > 0) {
        issues.push('Class cards not displayed');
    }

    // Check if athlete management list is populated
    const athletesList = document.getElementById('athletes-management-list');
    if (athletesList && cachedAthletes.length > 0) {
        const athleteItems = athletesList.querySelectorAll('.athlete-management-item');
        if (athleteItems.length !== cachedAthletes.length) {
            issues.push('Athlete management list out of sync');
        }
    }

    if (issues.length > 0) {
        console.warn('⚠️ Data synchronization issues detected:', issues);
        return { synchronized: false, issues };
    } else {
        console.log('✅ Data synchronization check passed');
        return { synchronized: true, issues: [] };
    }
}

/**
 * Auto-recovery function to fix common issues
 */
async function autoRecovery() {
    console.log('🚑 Running auto-recovery...');

    try {
        // Step 1: Validate and repair data
        const validationResult = await validateAndRepairData();

        // Step 2: Check synchronization
        const syncResult = checkDataSynchronization();

        // Step 3: Force refresh if needed
        if (!syncResult.synchronized) {
            console.log('🔄 Synchronization issues detected, forcing refresh...');
            await forceRefreshAllData();
        }

        // Step 4: Final validation
        const finalSync = checkDataSynchronization();

        if (finalSync.synchronized) {
            console.log('✅ Auto-recovery successful');
            return { success: true, repairsMade: validationResult.repairsMade };
        } else {
            console.warn('⚠️ Auto-recovery partially successful');
            return { success: false, issues: finalSync.issues };
        }

    } catch (error) {
        console.error('❌ Auto-recovery failed:', error);
        return { success: false, error };
    }
}

// Make functions available globally for onclick handlers
window.viewRoster = viewRoster;
window.viewAthleteProfile = viewAthleteProfile;
window.submitAthleteRun = submitAthleteRun;
window.addScore = addScore;
window.addMysteryScore = addMysteryScore;
window.addMiss = addMiss;
window.navigateToPage = navigateToPage;
window.closeAthleteModal = closeAthleteModal;
window.editAthlete = editAthlete;
window.deleteAthlete = deleteAthlete;

// Make recovery functions available globally for debugging
window.forceRefreshAllData = forceRefreshAllData;
window.validateAndRepairData = validateAndRepairData;
window.autoRecovery = autoRecovery;
window.checkDataSynchronization = checkDataSynchronization;
